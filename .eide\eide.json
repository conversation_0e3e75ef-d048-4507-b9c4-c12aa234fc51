{"name": "111", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Application", "files": [], "folders": [{"name": "MDK-ARM", "files": [{"path": "startup_stm32g431xx.s"}], "folders": []}, {"name": "User", "files": [], "folders": [{"name": "Core", "files": [{"path": "../Core/Src/main.c"}, {"path": "../Core/Src/gpio.c"}, {"path": "../Core/Src/adc.c"}, {"path": "../Core/Src/dma.c"}, {"path": "../Core/Src/tim.c"}, {"path": "../Core/Src/usart.c"}, {"path": "../Core/Src/stm32g4xx_it.c"}, {"path": "../Core/Src/stm32g4xx_hal_msp.c"}], "folders": []}]}]}, {"name": "Drivers", "files": [], "folders": [{"name": "STM32G4xx_HAL_Driver", "files": [{"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc_ex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_adc.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc_ex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ramfunc.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_gpio.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_exti.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma_ex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr_ex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_cortex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim_ex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart_ex.c"}], "folders": []}, {"name": "CMSIS", "files": [{"path": "../Core/Src/system_stm32g4xx.c"}], "folders": []}]}, {"name": "CODE", "files": [{"path": "../CODE/key.c"}, {"path": "../CODE/key.h"}, {"path": "../CODE/menu.c"}, {"path": "../CODE/menu.h"}, {"path": "../CODE/my_adc.c"}, {"path": "../CODE/my_adc.h"}, {"path": "../CODE/OLED.c"}, {"path": "../CODE/OLED.h"}, {"path": "../CODE/pid_yyds.c"}, {"path": "../CODE/pid_yyds.h"}, {"path": "../CODE/task.c"}, {"path": "../CODE/task.h"}, {"path": "../CODE/headfile.h"}], "folders": []}, {"name": "::CMSIS", "files": [], "folders": []}]}, "outDir": "build", "deviceName": null, "packDir": ".pack/Keil/STM32G4xx_DFP.1.1.0", "miscInfo": {"uid": "02fab59a4aa61fabdc0d565655f2e227"}, "targets": {"111": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M4", "floatingPointHardware": "single", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x8000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x20000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "Custom", "uploadConfig": {"bin": "", "commandLine": "", "eraseChipCommand": ""}, "uploadConfigMap": {"JLink": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}}, "custom_dep": {"name": "default", "incList": ["../Core/Inc", "../Drivers/STM32G4xx_HAL_Driver/Inc", "../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy", "../Drivers/CMSIS/Device/ST/STM32G4xx/Include", "../Drivers/CMSIS/Include", "../CODE", ".cmsis/include", "RTE/_111"], "libList": [], "defineList": ["USE_HAL_DRIVER", "STM32G431xx"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"output-format": "elf", "xo-base": "", "ro-base": "", "rw-base": ""}}}}}, "version": "3.5"}