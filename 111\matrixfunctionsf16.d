111\matrixfunctionsf16.o: C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/MatrixFunctionsF16.c
111\matrixfunctionsf16.o: C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_add_f16.c
111\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/matrix_functions_f16.h
111\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h
111\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h
111\matrixfunctionsf16.o: ../Drivers/CMSIS/Include/cmsis_compiler.h
111\matrixfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
111\matrixfunctionsf16.o: ../Drivers/CMSIS/Include/cmsis_armcc.h
111\matrixfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
111\matrixfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
111\matrixfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\float.h
111\matrixfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
111\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h
111\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/none.h
111\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/utils.h
111\matrixfunctionsf16.o: C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_sub_f16.c
111\matrixfunctionsf16.o: C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_trans_f16.c
111\matrixfunctionsf16.o: C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_scale_f16.c
111\matrixfunctionsf16.o: C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_mult_f16.c
111\matrixfunctionsf16.o: C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_vec_mult_f16.c
111\matrixfunctionsf16.o: C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_cmplx_trans_f16.c
111\matrixfunctionsf16.o: C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_cmplx_mult_f16.c
111\matrixfunctionsf16.o: C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_inverse_f16.c
111\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/matrix_utils.h
111\matrixfunctionsf16.o: C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_init_f16.c
111\matrixfunctionsf16.o: C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_cholesky_f16.c
111\matrixfunctionsf16.o: C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_solve_upper_triangular_f16.c
111\matrixfunctionsf16.o: C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_solve_lower_triangular_f16.c
111\matrixfunctionsf16.o: C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_qr_f16.c
111\matrixfunctionsf16.o: C:/Users/<USER>/AppData/Local/Arm/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_householder_f16.c
111\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/basic_math_functions_f16.h
111\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/fast_math_functions_f16.h
111\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/fast_math_functions.h
111\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/basic_math_functions.h
