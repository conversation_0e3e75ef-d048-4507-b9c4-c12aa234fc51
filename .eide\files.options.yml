##########################################################################################
#                        Append Compiler Options For Source Files
##########################################################################################

# syntax:
#   <your pattern>: <compiler options>
# For get pattern syntax, please refer to: https://www.npmjs.com/package/micromatch
#
# examples:
#   'main.cpp':           --cpp11 -Og ...
#   'src/*.c':            -gnu -O2 ...
#   'src/lib/**/*.cpp':   --cpp11 -Os ...
#   '!Application/*.c':   -O0
#   '**/*.c':             -O2 -gnu ...

version: "2.0"
options:
    "111":
        files: {}
        virtualPathFiles:
            <virtual_root>/CODE/key.c: ""
            <virtual_root>/CODE/menu.c: ""
            <virtual_root>/CODE/my_adc.c: ""
            <virtual_root>/CODE/OLED.c: ""
            <virtual_root>/CODE/pid_yyds.c: ""
            <virtual_root>/CODE/task.c: ""
