Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32g431xx.o(RESET) refers to startup_stm32g431xx.o(STACK) for __initial_sp
    startup_stm32g431xx.o(RESET) refers to startup_stm32g431xx.o(.text) for Reset_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.DMA1_Channel1_IRQHandler) for DMA1_Channel1_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.TIM1_BRK_TIM15_IRQHandler) for TIM1_BRK_TIM15_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.TIM6_DAC_IRQHandler) for TIM6_DAC_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.TIM7_IRQHandler) for TIM7_IRQHandler
    startup_stm32g431xx.o(.text) refers to system_stm32g4xx.o(i.SystemInit) for SystemInit
    startup_stm32g431xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) for HAL_PWREx_ControlVoltageScaling
    main.o(i.SystemClock_Config) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.SystemClock_Config) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32g4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM6_Init) for MX_TIM6_Init
    main.o(i.main) refers to tim.o(i.MX_TIM7_Init) for MX_TIM7_Init
    main.o(i.main) refers to tim.o(i.MX_TIM15_Init) for MX_TIM15_Init
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.main) refers to menu.o(i.Menu_Init) for Menu_Init
    main.o(i.main) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    main.o(i.main) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    main.o(i.main) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    main.o(i.main) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    main.o(i.main) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) for HAL_TIM_PWM_Start_IT
    main.o(i.main) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) for HAL_TIMEx_PWMN_Start_IT
    main.o(i.main) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) for HAL_TIM_IC_Start_IT
    main.o(i.main) refers to pid_yyds.o(i.PID_Init) for PID_Init
    main.o(i.main) refers to pid_control.o(i.PID_Control_Init) for PID_Control_Init
    main.o(i.main) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.main) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    main.o(i.main) refers to menu.o(i.Menu) for Menu
    main.o(i.main) refers to tim.o(.bss) for htim1
    main.o(i.main) refers to pid_yyds.o(.bss) for SPWM_Depth_PID
    main.o(i.main) refers to task.o(.data) for depth
    main.o(i.main) refers to usart.o(.data) for usart_buff
    main.o(i.main) refers to usart.o(.bss) for huart1
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(i.HAL_ADC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    adc.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.HAL_ADC_MspInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.bss) for hdma_adc1
    adc.o(i.MX_ADC1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.MX_ADC1_Init) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel) for HAL_ADCEx_MultiModeConfigChannel
    adc.o(i.MX_ADC1_Init) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for hadc1
    dma.o(i.MX_DMA_Init) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM15_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM15_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM15_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM15_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init) for HAL_TIM_IC_Init
    tim.o(i.MX_TIM15_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM15_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) for HAL_TIM_IC_ConfigChannel
    tim.o(i.MX_TIM15_Init) refers to tim.o(.bss) for htim15
    tim.o(i.MX_TIM1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableDeadTimePreload) for HAL_TIMEx_EnableDeadTimePreload
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM1_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for htim1
    tim.o(i.MX_TIM2_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM2_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init) for HAL_TIM_IC_Init
    tim.o(i.MX_TIM2_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) for HAL_TIM_IC_ConfigChannel
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for htim2
    tim.o(i.MX_TIM3_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM3_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for htim3
    tim.o(i.MX_TIM4_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM4_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for htim4
    tim.o(i.MX_TIM6_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM6_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM6_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM6_Init) refers to tim.o(.bss) for htim6
    tim.o(i.MX_TIM7_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM7_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM7_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM7_Init) refers to tim.o(.bss) for htim7
    usart.o(i.HAL_UART_MspDeInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_USART1_UART_Init) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) for HAL_UARTEx_SetTxFifoThreshold
    usart.o(i.MX_USART1_UART_Init) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) for HAL_UARTEx_SetRxFifoThreshold
    usart.o(i.MX_USART1_UART_Init) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode) for HAL_UARTEx_DisableFifoMode
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for huart1
    usart.o(i.fgetc) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Receive) for HAL_UART_Receive
    usart.o(i.fgetc) refers to usart.o(.bss) for huart1
    usart.o(i.fputc) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(i.fputc) refers to usart.o(.bss) for huart1
    stm32g4xx_it.o(i.DMA1_Channel1_IRQHandler) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32g4xx_it.o(i.DMA1_Channel1_IRQHandler) refers to adc.o(.bss) for hdma_adc1
    stm32g4xx_it.o(i.SysTick_Handler) refers to stm32g4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32g4xx_it.o(i.TIM1_BRK_TIM15_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32g4xx_it.o(i.TIM1_BRK_TIM15_IRQHandler) refers to tim.o(.bss) for htim1
    stm32g4xx_it.o(i.TIM2_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32g4xx_it.o(i.TIM2_IRQHandler) refers to tim.o(.bss) for htim2
    stm32g4xx_it.o(i.TIM6_DAC_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32g4xx_it.o(i.TIM6_DAC_IRQHandler) refers to tim.o(.bss) for htim6
    stm32g4xx_it.o(i.TIM7_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32g4xx_it.o(i.TIM7_IRQHandler) refers to tim.o(.bss) for htim7
    stm32g4xx_it.o(i.USART1_IRQHandler) refers to stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32g4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32g4xx_hal_msp.o(i.HAL_MspInit) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDDeadBattery) for HAL_PWREx_DisableUCPDDeadBattery
    stm32g4xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsDisableOngoing) for LL_ADC_IsDisableOngoing
    stm32g4xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32g4xx_hal_adc.o(i.ADC_DMAError) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32g4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32g4xx_hal_adc.o(i.ADC_Disable) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsDisableOngoing) for LL_ADC_IsDisableOngoing
    stm32g4xx_hal_adc.o(i.ADC_Disable) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc.o(i.ADC_Disable) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(i.ADC_Enable) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc.o(i.ADC_Enable) refers to stm32g4xx_hal_adc.o(i.LL_ADC_Enable) for LL_ADC_Enable
    stm32g4xx_hal_adc.o(i.ADC_Enable) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(i.ADC_Enable) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32g4xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels) for LL_ADC_SetAnalogWDMonitChannels
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_SetChannelSamplingTime) for LL_ADC_SetChannelSamplingTime
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_SetSamplingTimeCommonConfig) for LL_ADC_SetSamplingTimeCommonConfig
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_GetOffsetChannel) for LL_ADC_GetOffsetChannel
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_SetOffsetState) for LL_ADC_SetOffsetState
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_SetCommonPathInternalCh) for LL_ADC_SetCommonPathInternalCh
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode) for LL_ADC_GetMultimode
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback) for HAL_ADCEx_EndOfSamplingCallback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback) for HAL_ADCEx_LevelOutOfWindow2Callback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback) for HAL_ADCEx_LevelOutOfWindow3Callback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback) for HAL_ADCEx_InjectedQueueOverflowCallback
    stm32g4xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32g4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsInternalRegulatorEnabled) for LL_ADC_IsInternalRegulatorEnabled
    stm32g4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_Init) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode) for LL_ADC_GetMultimode
    stm32g4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32g4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode) for LL_ADC_GetMultimode
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32g4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_StartConversion) for LL_ADC_REG_StartConversion
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode) for LL_ADC_GetMultimode
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_StartConversion) for LL_ADC_REG_StartConversion
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode) for LL_ADC_GetMultimode
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32g4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_StartConversion) for LL_ADC_REG_StartConversion
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVoltageRegulator) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EnterADCDeepPowerDownMode) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetChannelSamplingTime) for LL_ADC_SetChannelSamplingTime
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetSamplingTimeCommonConfig) for LL_ADC_SetSamplingTimeCommonConfig
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_GetOffsetChannel) for LL_ADC_GetOffsetChannel
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetOffsetState) for LL_ADC_SetOffsetState
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetCommonPathInternalCh) for LL_ADC_SetCommonPathInternalCh
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32g4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_GetTrigAuto) for LL_ADC_INJ_GetTrigAuto
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_StartConversion) for LL_ADC_INJ_StartConversion
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32g4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_GetTrigAuto) for LL_ADC_INJ_GetTrigAuto
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_StartConversion) for LL_ADC_INJ_StartConversion
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal.o(i.HAL_DeInit) refers to stm32g4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32g4xx_hal.o(i.HAL_Delay) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal.o(i.HAL_Delay) refers to stm32g4xx_hal.o(.data) for uwTickFreq
    stm32g4xx_hal.o(i.HAL_GetTick) refers to stm32g4xx_hal.o(.data) for uwTick
    stm32g4xx_hal.o(i.HAL_GetTickFreq) refers to stm32g4xx_hal.o(.data) for uwTickFreq
    stm32g4xx_hal.o(i.HAL_GetTickPrio) refers to stm32g4xx_hal.o(.data) for uwTickPrio
    stm32g4xx_hal.o(i.HAL_IncTick) refers to stm32g4xx_hal.o(.data) for uwTick
    stm32g4xx_hal.o(i.HAL_Init) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32g4xx_hal.o(i.HAL_Init) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal.o(i.HAL_Init) refers to stm32g4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32g4xx_hal.o(i.HAL_InitTick) refers to stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32g4xx_hal.o(i.HAL_InitTick) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32g4xx_hal.o(i.HAL_InitTick) refers to stm32g4xx_hal.o(.data) for uwTickFreq
    stm32g4xx_hal.o(i.HAL_InitTick) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal.o(i.HAL_SetTickFreq) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal.o(i.HAL_SetTickFreq) refers to stm32g4xx_hal.o(.data) for uwTickFreq
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal_rcc.o(i.RCC_GetSysClockFreqFromPLLSource) for RCC_GetSysClockFreqFromPLLSource
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32g4xx.o(.constdata) for AHBPrescTable
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal.o(.data) for uwTickPrio
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32g4xx_hal.o(.data) for uwTickPrio
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32g4xx.o(.constdata) for APBPrescTable
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32g4xx.o(.constdata) for APBPrescTable
    stm32g4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32g4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32g4xx_hal.o(.data) for uwTickPrio
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback) for HAL_RCCEx_LSECSS_Callback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_BootLockConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_RDPConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_SecMemConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_WRPConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetPCROP) for FLASH_OB_GetPCROP
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetBootLock) for FLASH_OB_GetBootLock
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetSecMem) for FLASH_OB_GetSecMem
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_WRPConfig) for FLASH_OB_WRPConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_RDPConfig) for FLASH_OB_RDPConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig) for FLASH_OB_PCROPConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_SecMemConfig) for FLASH_OB_SecMemConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_BootLockConfig) for FLASH_OB_BootLockConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32g4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32g4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) for HAL_PWREx_DisableLowPowerRunMode
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode) for HAL_PWREx_EnableLowPowerRunMode
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode) for HAL_PWREx_EnterSTOP1Mode
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode) for HAL_PWREx_EnterSTOP0Mode
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM1Callback) for HAL_PWREx_PVM1Callback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM2Callback) for HAL_PWREx_PVM2Callback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback) for HAL_PWREx_PVM3Callback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback) for HAL_PWREx_PVM4Callback
    stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping) refers to stm32g4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32g4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to task.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to task.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback) for HAL_TIMEx_Break2Callback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EncoderIndexCallback) for HAL_TIMEx_EncoderIndexCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DirectionChangeCallback) for HAL_TIMEx_DirectionChangeCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_IndexErrorCallback) for HAL_TIMEx_IndexErrorCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_TransitionErrorCallback) for HAL_TIMEx_TransitionErrorCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to task.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32g4xx_hal_tim.o(i.TIM_DMAError) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to task.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32g4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32g4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32g4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigEncoderIndex) refers to stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ReArmBreakInput) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32g4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32g4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32g4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback) for HAL_UARTEx_WakeupCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback) for HAL_UARTEx_TxFifoEmptyCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback) for HAL_UARTEx_RxFifoFullCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32g4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32g4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_TxISR_16BIT_FIFOEN) for UART_TxISR_16BIT_FIFOEN
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_TxISR_8BIT_FIFOEN) for UART_TxISR_8BIT_FIFOEN
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_TxISR_16BIT) for UART_TxISR_16BIT
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_TxISR_8BIT) for UART_TxISR_8BIT
    stm32g4xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.UART_DMAError) refers to stm32g4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32g4xx_hal_uart.o(i.UART_DMAError) refers to stm32g4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32g4xx_hal_uart.o(i.UART_DMAError) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32g4xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to stm32g4xx_hal_uart.o(.constdata) for UARTPrescTable
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) for UART_RxISR_16BIT_FIFOEN
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) for UART_RxISR_8BIT_FIFOEN
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32g4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32g4xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_EnableFifoMode) refers to stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) refers to stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) refers to stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32g4xx_hal_uart_ex.o(i.UARTEx_Wakeup_AddressConfig) for UARTEx_Wakeup_AddressConfig
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) refers to stm32g4xx_hal_uart_ex.o(.constdata) for numerator
    system_stm32g4xx.o(i.SystemCoreClockUpdate) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    system_stm32g4xx.o(i.SystemCoreClockUpdate) refers to system_stm32g4xx.o(.constdata) for AHBPrescTable
    key.o(i.Encode_scan) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key.o(i.Encode_scan) refers to stm32g4xx_hal.o(i.HAL_Delay) for HAL_Delay
    key.o(i.key_scan) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key.o(i.key_scan) refers to stm32g4xx_hal.o(i.HAL_Delay) for HAL_Delay
    key.o(i.key_scan) refers to key.o(.data) for key_up
    menu.o(i.Menu) refers to key.o(i.Encode_scan) for Encode_scan
    menu.o(i.Menu) refers to oled.o(i.OLED_Clear) for OLED_Clear
    menu.o(i.Menu) refers to menu.o(i.display) for display
    menu.o(i.Menu) refers to strcmp.o(.text) for strcmp
    menu.o(i.Menu) refers to menu.o(.data) for key_flag
    menu.o(i.Menu_Init) refers to oled.o(i.OLED_Init) for OLED_Init
    menu.o(i.Menu_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    menu.o(i.Menu_Init) refers to oled.o(i.OLED_ColorTurn) for OLED_ColorTurn
    menu.o(i.Menu_Init) refers to oled.o(i.OLED_DisplayTurn) for OLED_DisplayTurn
    menu.o(i.Menu_Init) refers to menu.o(i.display) for display
    menu.o(i.Menu_Init) refers to menu.o(.data) for SelectItem_Current
    menu.o(i.display) refers to oled.o(i.OLED_Display_GB2312_string) for OLED_Display_GB2312_string
    menu.o(i.display) refers to menu.o(i.Compare_Min) for Compare_Min
    menu.o(i.display) refers to menu.o(.data) for SelectItem_Hidden
    menu.o(.data) refers to menu.o(.conststring) for .conststring
    menu.o(.data) refers to task.o(i.task1) for task1
    menu.o(.data) refers to task.o(i.task2) for task2
    menu.o(.data) refers to task.o(i.task3) for task3
    my_adc.o(i.ADC_Debug_Print) refers to my_adc.o(i.adc_multi_channel_sample) for adc_multi_channel_sample
    my_adc.o(i.ADC_Debug_Print) refers to printfa.o(i.__0printf) for __2printf
    my_adc.o(i.ADC_Debug_Print) refers to my_adc.o(i.calculate_rms_voltage) for calculate_rms_voltage
    my_adc.o(i.ADC_Debug_Print) refers to my_adc.o(i.calculate_rms_current) for calculate_rms_current
    my_adc.o(i.ADC_Debug_Print) refers to my_adc.o(i.calculate_dc_voltage) for calculate_dc_voltage
    my_adc.o(i.ADC_Debug_Print) refers to my_adc.o(i.calculate_dc_current) for calculate_dc_current
    my_adc.o(i.ADC_Debug_Print) refers to f2d.o(.text) for __aeabi_f2d
    my_adc.o(i.ADC_Debug_Print) refers to my_adc.o(.bss) for adc_buffer
    my_adc.o(i.ADC_Debug_Print) refers to my_adc.o(.data) for AC_current_out
    my_adc.o(i.ADC_System_Init) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) for HAL_ADCEx_Calibration_Start
    my_adc.o(i.ADC_System_Init) refers to printfa.o(i.__0printf) for __2printf
    my_adc.o(i.ADC_System_Init) refers to adc.o(.bss) for hadc1
    my_adc.o(i.ADC_System_Init) refers to my_adc.o(.bss) for filter_buffer_voltage
    my_adc.o(i.Calculate_ADC) refers to my_adc.o(i.adc_multi_channel_sample) for adc_multi_channel_sample
    my_adc.o(i.Calculate_ADC) refers to my_adc.o(i.calculate_rms_voltage) for calculate_rms_voltage
    my_adc.o(i.Calculate_ADC) refers to my_adc.o(i.calculate_rms_current) for calculate_rms_current
    my_adc.o(i.Calculate_ADC) refers to my_adc.o(i.sliding_average_filter_voltage) for sliding_average_filter_voltage
    my_adc.o(i.Calculate_ADC) refers to my_adc.o(i.sliding_average_filter_current) for sliding_average_filter_current
    my_adc.o(i.Calculate_ADC) refers to my_adc.o(.bss) for adc_buffer
    my_adc.o(i.Calculate_ADC) refers to my_adc.o(.data) for ac_voltage_raw
    my_adc.o(i.Calculate_All_ADC) refers to my_adc.o(i.Calculate_ADC) for Calculate_ADC
    my_adc.o(i.Calculate_All_ADC) refers to my_adc.o(i.Calculate_DC_ADC) for Calculate_DC_ADC
    my_adc.o(i.Calculate_DC_ADC) refers to my_adc.o(i.adc_multi_channel_sample) for adc_multi_channel_sample
    my_adc.o(i.Calculate_DC_ADC) refers to my_adc.o(i.calculate_dc_voltage) for calculate_dc_voltage
    my_adc.o(i.Calculate_DC_ADC) refers to my_adc.o(i.calculate_dc_current) for calculate_dc_current
    my_adc.o(i.Calculate_DC_ADC) refers to my_adc.o(i.sliding_average_filter_dc_voltage) for sliding_average_filter_dc_voltage
    my_adc.o(i.Calculate_DC_ADC) refers to my_adc.o(i.sliding_average_filter_dc_current) for sliding_average_filter_dc_current
    my_adc.o(i.Calculate_DC_ADC) refers to my_adc.o(.bss) for adc_buffer
    my_adc.o(i.Calculate_DC_ADC) refers to my_adc.o(.data) for dc_voltage_raw
    my_adc.o(i.adc_multi_channel_sample) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    my_adc.o(i.adc_multi_channel_sample) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    my_adc.o(i.adc_multi_channel_sample) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) for HAL_ADC_Stop_DMA
    my_adc.o(i.adc_multi_channel_sample) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_GetState) for HAL_ADC_GetState
    my_adc.o(i.adc_multi_channel_sample) refers to adc.o(.bss) for hadc1
    my_adc.o(i.calculate_dc_current) refers to my_adc.o(i.calculate_average) for calculate_average
    my_adc.o(i.calculate_dc_voltage) refers to my_adc.o(i.calculate_average) for calculate_average
    my_adc.o(i.calculate_rms_current) refers to my_adc.o(i.calculate_average) for calculate_average
    my_adc.o(i.calculate_rms_voltage) refers to my_adc.o(i.calculate_average) for calculate_average
    my_adc.o(i.sliding_average_filter_current) refers to my_adc.o(i.sliding_average_filter) for sliding_average_filter
    my_adc.o(i.sliding_average_filter_current) refers to my_adc.o(.data) for ac_current_index
    my_adc.o(i.sliding_average_filter_dc_current) refers to my_adc.o(i.sliding_average_filter) for sliding_average_filter
    my_adc.o(i.sliding_average_filter_dc_current) refers to my_adc.o(.data) for dc_current_index
    my_adc.o(i.sliding_average_filter_dc_voltage) refers to my_adc.o(i.sliding_average_filter) for sliding_average_filter
    my_adc.o(i.sliding_average_filter_dc_voltage) refers to my_adc.o(.data) for dc_voltage_index
    my_adc.o(i.sliding_average_filter_voltage) refers to my_adc.o(i.sliding_average_filter) for sliding_average_filter
    my_adc.o(i.sliding_average_filter_voltage) refers to my_adc.o(.data) for ac_voltage_index
    oled.o(i.Get_data_from_ROM) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled.o(i.Get_data_from_ROM) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    oled.o(i.INXERSR_OLED_ShowNum) refers to oled.o(i.OLED_Display_GB2312_string) for OLED_Display_GB2312_string
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ColorTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_Off) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisplayTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_128x64) refers to oled.o(i.OLED_address) for OLED_address
    oled.o(i.OLED_Display_128x64) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_16x16) refers to oled.o(i.OLED_address) for OLED_address
    oled.o(i.OLED_Display_16x16) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_5x7) refers to oled.o(i.OLED_address) for OLED_address
    oled.o(i.OLED_Display_5x7) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_8x16) refers to oled.o(i.OLED_address) for OLED_address
    oled.o(i.OLED_Display_8x16) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_GB2312_string) refers to oled.o(i.OLED_get_data_from_ROM) for OLED_get_data_from_ROM
    oled.o(i.OLED_Display_GB2312_string) refers to oled.o(i.OLED_Display_16x16) for OLED_Display_16x16
    oled.o(i.OLED_Display_GB2312_string) refers to oled.o(i.OLED_Display_8x16) for OLED_Display_8x16
    oled.o(i.OLED_Display_GB2312_string) refers to oled.o(.data) for fontaddr
    oled.o(i.OLED_Display_string_5x7) refers to oled.o(i.OLED_get_data_from_ROM) for OLED_get_data_from_ROM
    oled.o(i.OLED_Display_string_5x7) refers to oled.o(i.OLED_Display_5x7) for OLED_Display_5x7
    oled.o(i.OLED_Display_string_5x7) refers to oled.o(.data) for fontaddr
    oled.o(i.OLED_Init) refers to stm32g4xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Display_GB2312_string) for OLED_Display_GB2312_string
    oled.o(i.OLED_WR_Byte) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.delay) for delay
    oled.o(i.OLED_address) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_get_data_from_ROM) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled.o(i.OLED_get_data_from_ROM) refers to oled.o(i.Send_Command_to_ROM) for Send_Command_to_ROM
    oled.o(i.OLED_get_data_from_ROM) refers to oled.o(i.Get_data_from_ROM) for Get_data_from_ROM
    oled.o(i.Send_Command_to_ROM) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    task.o(i.HAL_TIM_IC_CaptureCallback) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue) for HAL_TIM_ReadCapturedValue
    task.o(i.HAL_TIM_IC_CaptureCallback) refers to task.o(.data) for bing_wang_flag
    task.o(i.HAL_TIM_PeriodElapsedCallback) refers to my_adc.o(i.Calculate_ADC) for Calculate_ADC
    task.o(i.HAL_TIM_PeriodElapsedCallback) refers to my_adc.o(i.Calculate_DC_ADC) for Calculate_DC_ADC
    task.o(i.HAL_TIM_PeriodElapsedCallback) refers to pid_control.o(i.PID_Control_Loop) for PID_Control_Loop
    task.o(i.HAL_TIM_PeriodElapsedCallback) refers to pid_control.o(i.DC_PID_Control_Loop) for DC_PID_Control_Loop
    task.o(i.HAL_TIM_PeriodElapsedCallback) refers to task.o(.data) for depth
    task.o(i.HAL_TIM_PeriodElapsedCallback) refers to task.o(.constdata) for sin_value
    task.o(i.dc_task_init) refers to pid_control.o(i.DC_PID_Control_Init) for DC_PID_Control_Init
    task.o(i.dc_task_init) refers to printfa.o(i.__0printf) for __2printf
    task.o(i.dc_task_loop) refers to my_adc.o(i.Calculate_DC_ADC) for Calculate_DC_ADC
    task.o(i.dc_task_loop) refers to pid_control.o(i.DC_PID_Control_Loop) for DC_PID_Control_Loop
    task.o(i.dc_task_status) refers to pid_control.o(i.DC_Get_PID_Status) for DC_Get_PID_Status
    task.o(i.dc_task_status) refers to printfa.o(i.__0printf) for __2printf
    task.o(i.dc_task_status) refers to f2d.o(.text) for __aeabi_f2d
    task.o(i.dc_task_status) refers to my_adc.o(.data) for DC_voltage_out
    task.o(i.display_basic_info) refers to oled.o(i.OLED_Display_GB2312_string) for OLED_Display_GB2312_string
    task.o(i.display_basic_info) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    task.o(i.display_basic_info) refers to my_adc.o(.data) for AC_voltage_out
    task.o(i.display_dc_basic_info) refers to oled.o(i.OLED_Display_GB2312_string) for OLED_Display_GB2312_string
    task.o(i.display_dc_basic_info) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    task.o(i.display_dc_basic_info) refers to my_adc.o(.data) for DC_voltage_out
    task.o(i.display_dc_pid_status) refers to pid_control.o(i.DC_Get_PID_Status) for DC_Get_PID_Status
    task.o(i.display_dc_pid_status) refers to oled.o(i.OLED_Display_GB2312_string) for OLED_Display_GB2312_string
    task.o(i.display_dc_pid_status) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    task.o(i.handle_menu_return) refers to pid_control.o(i.PID_Control_Disable_All) for PID_Control_Disable_All
    task.o(i.handle_menu_return) refers to oled.o(i.OLED_Clear) for OLED_Clear
    task.o(i.handle_menu_return) refers to menu.o(i.display) for display
    task.o(i.handle_menu_return) refers to task.o(.data) for key_num
    task.o(i.handle_menu_return) refers to menu.o(.data) for key_flag
    task.o(i.task1) refers to task.o(i.task_init) for task_init
    task.o(i.task1) refers to task.o(i.display_dc_basic_info) for display_dc_basic_info
    task.o(i.task1) refers to task.o(i.display_dc_pid_status) for display_dc_pid_status
    task.o(i.task1) refers to pid_control.o(i.DC_Enable_Constant_Voltage) for DC_Enable_Constant_Voltage
    task.o(i.task1) refers to pid_control.o(i.DC_Enable_Constant_Current) for DC_Enable_Constant_Current
    task.o(i.task1) refers to pid_control.o(i.DC_Disable_PID_Control) for DC_Disable_PID_Control
    task.o(i.task1) refers to task.o(i.handle_menu_return) for handle_menu_return
    task.o(i.task1) refers to task.o(.data) for key_num
    task.o(i.task2) refers to task.o(i.task_init) for task_init
    task.o(i.task2) refers to task.o(i.display_basic_info) for display_basic_info
    task.o(i.task2) refers to oled.o(i.OLED_Display_GB2312_string) for OLED_Display_GB2312_string
    task.o(i.task2) refers to pid_control.o(i.PID_Control_Enable_Voltage) for PID_Control_Enable_Voltage
    task.o(i.task2) refers to pid_control.o(i.PID_Control_Disable_All) for PID_Control_Disable_All
    task.o(i.task2) refers to task.o(i.handle_menu_return) for handle_menu_return
    task.o(i.task2) refers to pid_control.o(.data) for voltage_control_flag
    task.o(i.task2) refers to task.o(.data) for key_num
    task.o(i.task3) refers to task.o(i.task_init) for task_init
    task.o(i.task3) refers to task.o(i.display_basic_info) for display_basic_info
    task.o(i.task3) refers to oled.o(i.OLED_Display_GB2312_string) for OLED_Display_GB2312_string
    task.o(i.task3) refers to pid_control.o(i.PID_Control_Enable_Voltage) for PID_Control_Enable_Voltage
    task.o(i.task3) refers to pid_control.o(i.PID_Control_Disable_All) for PID_Control_Disable_All
    task.o(i.task3) refers to pid_control.o(i.PID_Control_Enable_Current) for PID_Control_Enable_Current
    task.o(i.task3) refers to task.o(i.handle_menu_return) for handle_menu_return
    task.o(i.task3) refers to task.o(.data) for bing_wang_flag
    task.o(i.task3) refers to pid_control.o(.data) for current_control_flag
    task.o(i.task_init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    task.o(i.task_init) refers to key.o(i.key_scan) for key_scan
    task.o(i.task_init) refers to task.o(.data) for oled_flag
    task.o(i.task_init) refers to menu.o(.data) for key_flag
    pid_yyds.o(i.PID_Debug_Info) refers to printfa.o(i.__0printf) for __2printf
    pid_yyds.o(i.PID_Debug_Info) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.DC_Disable_PID_Control) refers to pid_control.o(i.DC_Set_PWM_Output) for DC_Set_PWM_Output
    pid_control.o(i.DC_Disable_PID_Control) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.DC_Disable_PID_Control) refers to pid_control.o(.data) for dc_pid_enable
    pid_control.o(i.DC_Enable_Constant_Current) refers to pid_control.o(i.DC_PID_Reset) for DC_PID_Reset
    pid_control.o(i.DC_Enable_Constant_Current) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.DC_Enable_Constant_Current) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.DC_Enable_Constant_Current) refers to pid_control.o(.data) for dc_target_current
    pid_control.o(i.DC_Enable_Constant_Current) refers to pid_control.o(.bss) for dc_current_pid
    pid_control.o(i.DC_Enable_Constant_Voltage) refers to pid_control.o(i.DC_PID_Reset) for DC_PID_Reset
    pid_control.o(i.DC_Enable_Constant_Voltage) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.DC_Enable_Constant_Voltage) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.DC_Enable_Constant_Voltage) refers to pid_control.o(.data) for dc_target_voltage
    pid_control.o(i.DC_Enable_Constant_Voltage) refers to pid_control.o(.bss) for dc_voltage_pid
    pid_control.o(i.DC_Get_PID_Status) refers to pid_control.o(.data) for dc_pid_mode
    pid_control.o(i.DC_Get_PID_Status) refers to my_adc.o(.data) for DC_voltage_out
    pid_control.o(i.DC_PID_Calculate) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    pid_control.o(i.DC_PID_Control_Init) refers to pid_control.o(i.DC_PID_Init) for DC_PID_Init
    pid_control.o(i.DC_PID_Control_Init) refers to pid_control.o(i.DC_Set_PWM_Output) for DC_Set_PWM_Output
    pid_control.o(i.DC_PID_Control_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    pid_control.o(i.DC_PID_Control_Init) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.DC_PID_Control_Init) refers to pid_control.o(.bss) for dc_voltage_pid
    pid_control.o(i.DC_PID_Control_Init) refers to tim.o(.bss) for htim1
    pid_control.o(i.DC_PID_Control_Init) refers to pid_control.o(.data) for dc_pid_mode
    pid_control.o(i.DC_PID_Control_Loop) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    pid_control.o(i.DC_PID_Control_Loop) refers to pid_control.o(i.DC_PID_Calculate) for DC_PID_Calculate
    pid_control.o(i.DC_PID_Control_Loop) refers to pid_control.o(i.DC_Set_PWM_Output) for DC_Set_PWM_Output
    pid_control.o(i.DC_PID_Control_Loop) refers to pid_control.o(.data) for last_update_time
    pid_control.o(i.DC_PID_Control_Loop) refers to my_adc.o(.data) for DC_voltage_out
    pid_control.o(i.DC_PID_Control_Loop) refers to pid_control.o(.bss) for dc_voltage_pid
    pid_control.o(i.DC_PID_Init) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    pid_control.o(i.DC_PID_Reset) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    pid_control.o(i.DC_PID_Reset_All) refers to pid_control.o(i.DC_PID_Reset) for DC_PID_Reset
    pid_control.o(i.DC_PID_Reset_All) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.DC_PID_Reset_All) refers to pid_control.o(.bss) for dc_voltage_pid
    pid_control.o(i.DC_Set_PID_Output_Limits) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.DC_Set_PID_Output_Limits) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.DC_Set_PID_Output_Limits) refers to pid_control.o(.bss) for dc_voltage_pid
    pid_control.o(i.DC_Set_PID_Parameters) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.DC_Set_PID_Parameters) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.DC_Set_PID_Parameters) refers to pid_control.o(.bss) for dc_voltage_pid
    pid_control.o(i.DC_Set_PWM_Output) refers to tim.o(.bss) for htim1
    pid_control.o(i.DC_Set_PWM_Output) refers to pid_control.o(.data) for dc_pwm_output
    pid_control.o(i.PID_Control_Check_Stuck) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Check_Stuck) refers to pid_yyds.o(.bss) for SPWM_Depth_PID
    pid_control.o(i.PID_Control_Current_Process) refers to pid_yyds.o(i.PID_Current_Control) for PID_Current_Control
    pid_control.o(i.PID_Control_Current_Process) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.PID_Control_Current_Process) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Current_Process) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Current_Process) refers to task.o(.data) for depth
    pid_control.o(i.PID_Control_Current_Process) refers to my_adc.o(.data) for AC_current_out
    pid_control.o(i.PID_Control_Current_Process) refers to pid_yyds.o(.bss) for Current_Control_PID
    pid_control.o(i.PID_Control_Disable_All) refers to pid_control.o(i.PID_Control_Set_Mode) for PID_Control_Set_Mode
    pid_control.o(i.PID_Control_Enable_Current) refers to pid_control.o(i.PID_Control_Set_Mode) for PID_Control_Set_Mode
    pid_control.o(i.PID_Control_Enable_Current) refers to pid_yyds.o(i.PID_Reset) for PID_Reset
    pid_control.o(i.PID_Control_Enable_Current) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.PID_Control_Enable_Current) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Enable_Current) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Enable_Current) refers to pid_control.o(.data) for target_current
    pid_control.o(i.PID_Control_Enable_Current) refers to pid_yyds.o(.bss) for Current_Control_PID
    pid_control.o(i.PID_Control_Enable_Voltage) refers to pid_control.o(i.PID_Control_Set_Mode) for PID_Control_Set_Mode
    pid_control.o(i.PID_Control_Enable_Voltage) refers to pid_yyds.o(i.PID_Reset) for PID_Reset
    pid_control.o(i.PID_Control_Enable_Voltage) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.PID_Control_Enable_Voltage) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Enable_Voltage) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Enable_Voltage) refers to pid_yyds.o(.bss) for SPWM_Depth_PID
    pid_control.o(i.PID_Control_Get_Mode) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Get_Status) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Get_Status) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.PID_Control_Get_Status) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Get_Status) refers to my_adc.o(.data) for AC_voltage_out
    pid_control.o(i.PID_Control_Get_Status) refers to pid_yyds.o(.bss) for SPWM_Depth_PID
    pid_control.o(i.PID_Control_Get_Status) refers to task.o(.data) for depth
    pid_control.o(i.PID_Control_Init) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Init) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Init) refers to pid_control.o(.data) for voltage_control_flag
    pid_control.o(i.PID_Control_Loop) refers to pid_control.o(i.PID_Control_Voltage_Process) for PID_Control_Voltage_Process
    pid_control.o(i.PID_Control_Loop) refers to pid_control.o(i.PID_Control_Current_Process) for PID_Control_Current_Process
    pid_control.o(i.PID_Control_Loop) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Reset) refers to pid_yyds.o(i.PID_Reset) for PID_Reset
    pid_control.o(i.PID_Control_Reset) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Reset) refers to pid_yyds.o(.bss) for SPWM_Depth_PID
    pid_control.o(i.PID_Control_Reset) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Set_Mode) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Set_Mode) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Set_Mode) refers to pid_control.o(.data) for voltage_control_flag
    pid_control.o(i.PID_Control_Set_Target_Current) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.PID_Control_Set_Target_Current) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Set_Target_Current) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Set_Target_Current) refers to pid_control.o(.data) for target_current
    pid_control.o(i.PID_Control_Set_Target_Current) refers to pid_yyds.o(.bss) for Current_Control_PID
    pid_control.o(i.PID_Control_Set_Target_Voltage) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.PID_Control_Set_Target_Voltage) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Set_Target_Voltage) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Set_Target_Voltage) refers to pid_yyds.o(.bss) for SPWM_Depth_PID
    pid_control.o(i.PID_Control_Voltage_Process) refers to pid_yyds.o(i.PID_SPWM_Depth_Control) for PID_SPWM_Depth_Control
    pid_control.o(i.PID_Control_Voltage_Process) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.PID_Control_Voltage_Process) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Voltage_Process) refers to pid_yyds.o(i.PID_Debug_Info) for PID_Debug_Info
    pid_control.o(i.PID_Control_Voltage_Process) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Voltage_Process) refers to task.o(.data) for depth
    pid_control.o(i.PID_Control_Voltage_Process) refers to my_adc.o(.data) for AC_voltage_out
    pid_control.o(i.PID_Control_Voltage_Process) refers to pid_yyds.o(.bss) for SPWM_Depth_PID
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32g431xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32g431xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32g431xx.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.data), (10 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (48 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (204 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (48 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (52 bytes).
    Removing usart.o(i.fgetc), (32 bytes).
    Removing stm32g4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (824 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_DeInit), (452 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_GetError), (6 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_GetValue), (8 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler), (816 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_PollForConversion), (312 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (258 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_Start), (284 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_StartSampling), (18 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_Start_IT), (468 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_Stop), (74 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_StopSampling), (18 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (86 bytes).
    Removing stm32g4xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels), (48 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_GetValue), (28 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue), (138 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start), (176 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue), (60 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVoltageRegulator), (40 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue), (58 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback), (2 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EnterADCDeepPowerDownMode), (44 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (1580 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (62 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (320 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback), (2 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (264 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (348 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (92 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (104 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback), (2 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback), (2 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (260 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (292 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA), (292 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop), (100 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA), (152 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT), (112 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_GetOffsetChannel), (18 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_GetTrigAuto), (10 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing), (10 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_StartConversion), (20 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetChannelSamplingTime), (40 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetCommonPathInternalCh), (12 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetOffsetState), (22 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetSamplingTimeCommonConfig), (12 bytes).
    Removing stm32g4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DeInit), (60 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32g4xx_hal.o(i.HAL_ResumeTick), (18 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_CCMSRAMErase), (28 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_CCMSRAM_WriteProtectionEnable), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableIOSwitchBooster), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableIOSwitchVDD), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableMemorySwappingBank), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableVREFBUF), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableIOSwitchBooster), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableIOSwitchVDD), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableMemorySwappingBank), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF), (56 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_VREFBUF_TrimmingConfig), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SetTickFreq), (48 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SuspendTick), (18 bytes).
    Removing stm32g4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit), (220 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_DisableLSECSS), (24 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (20 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_EnableLSECSS), (24 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (68 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (272 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (108 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (36 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig), (84 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSGetSynchronizationInfo), (44 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (20 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization), (196 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler), (144 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO), (112 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSECSS), (32 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO), (180 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS), (24 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS_IT), (64 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (176 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (1560 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler), (36 bytes).
    Removing stm32g4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (40 bytes).
    Removing stm32g4xx_hal_flash.o(i.FLASH_Program_Fast), (64 bytes).
    Removing stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (140 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (344 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Lock), (32 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (28 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (32 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (48 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Program), (192 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (184 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Unlock), (48 bytes).
    Removing stm32g4xx_hal_flash.o(.data), (32 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (120 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_BootLockConfig), (60 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetBootLock), (16 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetPCROP), (64 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (28 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetSecMem), (20 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (56 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig), (124 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_RDPConfig), (60 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_SecMemConfig), (68 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (316 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_WRPConfig), (80 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase), (44 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_DisableDebugger), (20 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_EnableDebugger), (20 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_EnableSecMemProtection), (24 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (256 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (192 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (116 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (176 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_DisableRunPowerDown), (40 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_EnableRunPowerDown), (40 bytes).
    Removing stm32g4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (300 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (28 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (46 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (20 bytes).
    Removing stm32g4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (148 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (36 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (36 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (192 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GetPending), (44 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (56 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (24 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (228 bytes).
    Removing stm32g4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit), (188 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_GetError), (6 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_GetState), (8 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (328 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (90 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_Start), (114 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (106 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxRequestGenerator), (86 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxSync), (88 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_DisableMuxRequestGenerator), (32 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_EnableMuxRequestGenerator), (32 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_MUX_IRQHandler), (102 bytes).
    Removing stm32g4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (184 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DeInit), (28 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (40 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (80 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (44 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (28 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigPVM), (668 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBatteryCharging), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullDown), (132 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullUp), (132 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableInternalWakeUpLine), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode), (84 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM1), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM2), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM3), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM4), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePullUpPullDownConfig), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableSRAM2ContentRetention), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDStandbyMode), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBatteryCharging), (32 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullDown), (208 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullUp), (208 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableInternalWakeUpLine), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM1), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM2), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM3), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM4), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePullUpPullDownConfig), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableSRAM2ContentRetention), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUCPDDeadBattery), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUCPDStandbyMode), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSHUTDOWNMode), (44 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode), (60 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode), (64 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (52 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler), (128 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM1Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM2Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback), (2 bytes).
    Removing stm32g4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (92 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (28 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_Enable), (48 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (28 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (36 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (68 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (48 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (48 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (148 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (36 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (68 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (40 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (128 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (236 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (50 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (70 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (62 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (668 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (22 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (476 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (476 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (42 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (140 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (42 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (140 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (96 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (556 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (252 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (238 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (304 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (280 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (54 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (6 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (58 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (128 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start), (348 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (640 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (162 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (270 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (246 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (144 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (128 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Init), (110 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start), (332 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (672 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (416 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (228 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (336 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (312 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (320 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (96 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (102 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (176 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (200 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (196 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (220 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (128 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (672 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (228 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (336 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (312 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (108 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (108 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt), (122 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (64 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (104 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (64 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMAError), (94 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (26 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (14 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMATriggerCplt), (26 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (14 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (260 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigAsymmetricalDeadTime), (20 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakInput), (224 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (180 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (208 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (180 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigDeadTime), (20 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigEncoderIndex), (92 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigSlaveModePreload), (20 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableAsymmetricalDeadTime), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableDeadTimePreload), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableEncoderFirstIndex), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableEncoderIndex), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableSlaveModePreload), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisarmBreakInput), (94 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DitheringDisable), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DitheringEnable), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableAsymmetricalDeadTime), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableEncoderFirstIndex), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableEncoderIndex), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableSlaveModePreload), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (38 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_GroupChannel5), (72 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (96 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (248 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (204 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (268 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (216 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (78 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (84 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (90 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (232 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (552 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (328 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (136 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (242 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (248 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OC_ConfigPulseOnCompare), (80 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (118 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (142 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (138 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (162 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (232 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (552 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (136 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (242 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (248 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ReArmBreakInput), (152 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (52 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_TISelection), (180 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (20 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (20 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (104 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (86 bytes).
    Removing stm32g4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (102 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (102 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (124 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_LIN_Init), (160 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_LIN_SendBreak), (62 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode), (72 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode), (72 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (14 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (148 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Abort), (336 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive), (224 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (256 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (176 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (200 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT), (396 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DMAPause), (164 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DMAResume), (148 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop), (190 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DeInit), (76 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DisableReceiverTimeout), (88 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_EnableReceiverTimeout), (88 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_GetError), (8 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_GetState), (16 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Receive), (274 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (92 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_ReceiverTimeout_Config), (28 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (160 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (172 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMAError), (94 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMAReceiveCplt), (180 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMARxAbortCallback), (82 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMARxHalfCplt), (38 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (48 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMATransmitCplt), (86 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMATxAbortCallback), (92 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMATxHalfCplt), (14 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (46 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_EndTxTransfer), (64 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA), (200 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_TxISR_16BIT), (98 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_TxISR_16BIT_FIFOEN), (130 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_TxISR_8BIT), (92 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_TxISR_8BIT_FIFOEN), (126 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set), (64 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init), (152 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableStopMode), (64 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_EnableFifoMode), (88 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_EnableStopMode), (64 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_GetRxEventType), (6 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle), (362 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (98 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT), (96 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig), (144 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.UARTEx_Wakeup_AddressConfig), (38 bytes).
    Removing system_stm32g4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32g4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32g4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32g4xx.o(i.SystemCoreClockUpdate), (156 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing menu.o(.rev16_text), (4 bytes).
    Removing menu.o(.revsh_text), (4 bytes).
    Removing menu.o(.rrx_text), (6 bytes).
    Removing menu.o(.bss), (20 bytes).
    Removing my_adc.o(.rev16_text), (4 bytes).
    Removing my_adc.o(.revsh_text), (4 bytes).
    Removing my_adc.o(.rrx_text), (6 bytes).
    Removing my_adc.o(i.ADC_Debug_Print), (880 bytes).
    Removing my_adc.o(i.ADC_System_Init), (172 bytes).
    Removing my_adc.o(i.Calculate_All_ADC), (12 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.INXERSR_OLED_ShowNum), (320 bytes).
    Removing oled.o(i.OLED_DisPlay_Off), (28 bytes).
    Removing oled.o(i.OLED_DisPlay_On), (28 bytes).
    Removing oled.o(i.OLED_Display_128x64), (66 bytes).
    Removing oled.o(i.OLED_Display_5x7), (58 bytes).
    Removing oled.o(i.OLED_Display_string_5x7), (140 bytes).
    Removing task.o(.rev16_text), (4 bytes).
    Removing task.o(.revsh_text), (4 bytes).
    Removing task.o(.rrx_text), (6 bytes).
    Removing task.o(i.dc_task_init), (40 bytes).
    Removing task.o(i.dc_task_loop), (12 bytes).
    Removing task.o(i.dc_task_status), (384 bytes).
    Removing pid_yyds.o(.rev16_text), (4 bytes).
    Removing pid_yyds.o(.revsh_text), (4 bytes).
    Removing pid_yyds.o(.rrx_text), (6 bytes).
    Removing pid_yyds.o(i.PID_Auto_Tune), (344 bytes).
    Removing pid_yyds.o(i.PID_Current_Auto_Tune), (564 bytes).
    Removing pid_yyds.o(i.PID_Incremental), (296 bytes).
    Removing pid_yyds.o(i.PID_Incremental_Advanced), (364 bytes).
    Removing pid_yyds.o(i.PID_Position), (288 bytes).
    Removing pid_yyds.o(i.PID_SPWM_Auto_Tune), (516 bytes).
    Removing pid_control.o(.rev16_text), (4 bytes).
    Removing pid_control.o(.revsh_text), (4 bytes).
    Removing pid_control.o(.rrx_text), (6 bytes).
    Removing pid_control.o(i.DC_PID_Control_Init), (140 bytes).
    Removing pid_control.o(i.DC_PID_Init), (116 bytes).
    Removing pid_control.o(i.DC_PID_Reset_All), (64 bytes).
    Removing pid_control.o(i.DC_Set_PID_Output_Limits), (372 bytes).
    Removing pid_control.o(i.DC_Set_PID_Parameters), (304 bytes).
    Removing pid_control.o(i.PID_Control_Check_Stuck), (104 bytes).
    Removing pid_control.o(i.PID_Control_Get_Mode), (12 bytes).
    Removing pid_control.o(i.PID_Control_Get_Status), (524 bytes).
    Removing pid_control.o(i.PID_Control_Reset), (80 bytes).
    Removing pid_control.o(i.PID_Control_Set_Target_Current), (96 bytes).
    Removing pid_control.o(i.PID_Control_Set_Target_Voltage), (88 bytes).

589 unused section(s) (total 56058 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32g4xx_hal_msp.c          0x00000000   Number         0  stm32g4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32g4xx_it.c               0x00000000   Number         0  stm32g4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32g4xx.c           0x00000000   Number         0  system_stm32g4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal.c 0x00000000   Number         0  stm32g4xx_hal.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc.c 0x00000000   Number         0  stm32g4xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc_ex.c 0x00000000   Number         0  stm32g4xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_cortex.c 0x00000000   Number         0  stm32g4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma.c 0x00000000   Number         0  stm32g4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma_ex.c 0x00000000   Number         0  stm32g4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_exti.c 0x00000000   Number         0  stm32g4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash.c 0x00000000   Number         0  stm32g4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ex.c 0x00000000   Number         0  stm32g4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32g4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_gpio.c 0x00000000   Number         0  stm32g4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr.c 0x00000000   Number         0  stm32g4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32g4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc.c 0x00000000   Number         0  stm32g4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32g4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim.c 0x00000000   Number         0  stm32g4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim_ex.c 0x00000000   Number         0  stm32g4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart.c 0x00000000   Number         0  stm32g4xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart_ex.c 0x00000000   Number         0  stm32g4xx_hal_uart_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_adc.c 0x00000000   Number         0  stm32g4xx_ll_adc.o ABSOLUTE
    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\CODE\OLED.c                           0x00000000   Number         0  oled.o ABSOLUTE
    ..\CODE\key.c                            0x00000000   Number         0  key.o ABSOLUTE
    ..\CODE\menu.c                           0x00000000   Number         0  menu.o ABSOLUTE
    ..\CODE\my_adc.c                         0x00000000   Number         0  my_adc.o ABSOLUTE
    ..\CODE\pid_control.c                    0x00000000   Number         0  pid_control.o ABSOLUTE
    ..\CODE\pid_yyds.c                       0x00000000   Number         0  pid_yyds.o ABSOLUTE
    ..\CODE\task.c                           0x00000000   Number         0  task.o ABSOLUTE
    ..\Core\Src\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32g4xx_hal_msp.c          0x00000000   Number         0  stm32g4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32g4xx_it.c               0x00000000   Number         0  stm32g4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32g4xx.c           0x00000000   Number         0  system_stm32g4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal.c 0x00000000   Number         0  stm32g4xx_hal.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_adc.c 0x00000000   Number         0  stm32g4xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_adc_ex.c 0x00000000   Number         0  stm32g4xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cortex.c 0x00000000   Number         0  stm32g4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma.c 0x00000000   Number         0  stm32g4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma_ex.c 0x00000000   Number         0  stm32g4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_exti.c 0x00000000   Number         0  stm32g4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash.c 0x00000000   Number         0  stm32g4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash_ex.c 0x00000000   Number         0  stm32g4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32g4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_gpio.c 0x00000000   Number         0  stm32g4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr.c 0x00000000   Number         0  stm32g4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32g4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc.c 0x00000000   Number         0  stm32g4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32g4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_tim.c 0x00000000   Number         0  stm32g4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_tim_ex.c 0x00000000   Number         0  stm32g4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_uart.c 0x00000000   Number         0  stm32g4xx_hal_uart.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_uart_ex.c 0x00000000   Number         0  stm32g4xx_hal_uart_ex.o ABSOLUTE
    ..\\CODE\\OLED.c                         0x00000000   Number         0  oled.o ABSOLUTE
    ..\\CODE\\key.c                          0x00000000   Number         0  key.o ABSOLUTE
    ..\\CODE\\menu.c                         0x00000000   Number         0  menu.o ABSOLUTE
    ..\\CODE\\my_adc.c                       0x00000000   Number         0  my_adc.o ABSOLUTE
    ..\\CODE\\pid_control.c                  0x00000000   Number         0  pid_control.o ABSOLUTE
    ..\\CODE\\pid_yyds.c                     0x00000000   Number         0  pid_yyds.o ABSOLUTE
    ..\\CODE\\task.c                         0x00000000   Number         0  task.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32g431xx.s                    0x00000000   Number         0  startup_stm32g431xx.o ABSOLUTE
    RESET                                    0x08000000   Section      472  startup_stm32g431xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001d8   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001d8   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001dc   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001e0   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001e0   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001e0   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x080001e8   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x080001ec   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x080001ec   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x080001ec   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080001ec   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001f0   Section       36  startup_stm32g431xx.o(.text)
    $v0                                      0x080001f0   Number         0  startup_stm32g431xx.o(.text)
    .text                                    0x08000214   Section        0  uldiv.o(.text)
    .text                                    0x08000276   Section        0  memseta.o(.text)
    .text                                    0x0800029a   Section        0  strcmp.o(.text)
    .text                                    0x080002b6   Section        0  f2d.o(.text)
    .text                                    0x080002dc   Section        0  uidiv.o(.text)
    .text                                    0x08000308   Section        0  llshl.o(.text)
    .text                                    0x08000326   Section        0  llushr.o(.text)
    .text                                    0x08000346   Section        0  iusefp.o(.text)
    .text                                    0x08000346   Section        0  dadd.o(.text)
    .text                                    0x08000494   Section        0  dmul.o(.text)
    .text                                    0x08000578   Section        0  ddiv.o(.text)
    .text                                    0x08000656   Section        0  dfixul.o(.text)
    .text                                    0x08000688   Section       48  cdrcmple.o(.text)
    .text                                    0x080006b8   Section       36  init.o(.text)
    .text                                    0x080006dc   Section        0  llsshr.o(.text)
    .text                                    0x08000700   Section        0  depilogue.o(.text)
    .text                                    0x080007ba   Section        0  __dczerorl2.o(.text)
    i.ADC_ConversionStop                     0x08000810   Section        0  stm32g4xx_hal_adc.o(i.ADC_ConversionStop)
    i.ADC_DMAConvCplt                        0x0800092c   Section        0  stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt)
    i.ADC_DMAError                           0x080009be   Section        0  stm32g4xx_hal_adc.o(i.ADC_DMAError)
    i.ADC_DMAHalfConvCplt                    0x080009dc   Section        0  stm32g4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    i.ADC_Disable                            0x080009ec   Section        0  stm32g4xx_hal_adc.o(i.ADC_Disable)
    i.ADC_Enable                             0x08000a80   Section        0  stm32g4xx_hal_adc.o(i.ADC_Enable)
    i.BusFault_Handler                       0x08000b48   Section        0  stm32g4xx_it.o(i.BusFault_Handler)
    i.Calculate_ADC                          0x08000b4c   Section        0  my_adc.o(i.Calculate_ADC)
    i.Calculate_DC_ADC                       0x08000c3c   Section        0  my_adc.o(i.Calculate_DC_ADC)
    i.Compare_Min                            0x08000d20   Section        0  menu.o(i.Compare_Min)
    Compare_Min                              0x08000d21   Thumb Code    14  menu.o(i.Compare_Min)
    i.DC_Disable_PID_Control                 0x08000d30   Section        0  pid_control.o(i.DC_Disable_PID_Control)
    i.DC_Enable_Constant_Current             0x08000d6c   Section        0  pid_control.o(i.DC_Enable_Constant_Current)
    i.DC_Enable_Constant_Voltage             0x08000de4   Section        0  pid_control.o(i.DC_Enable_Constant_Voltage)
    i.DC_Get_PID_Status                      0x08000e5c   Section        0  pid_control.o(i.DC_Get_PID_Status)
    i.DC_PID_Calculate                       0x08000ed8   Section        0  pid_control.o(i.DC_PID_Calculate)
    DC_PID_Calculate                         0x08000ed9   Thumb Code   296  pid_control.o(i.DC_PID_Calculate)
    i.DC_PID_Control_Loop                    0x0800100c   Section        0  pid_control.o(i.DC_PID_Control_Loop)
    i.DC_PID_Reset                           0x080010b8   Section        0  pid_control.o(i.DC_PID_Reset)
    DC_PID_Reset                             0x080010b9   Thumb Code    52  pid_control.o(i.DC_PID_Reset)
    i.DC_Set_PWM_Output                      0x080010f0   Section        0  pid_control.o(i.DC_Set_PWM_Output)
    i.DMA1_Channel1_IRQHandler               0x08001110   Section        0  stm32g4xx_it.o(i.DMA1_Channel1_IRQHandler)
    i.DMA_CalcDMAMUXChannelBaseAndMask       0x08001120   Section        0  stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask)
    DMA_CalcDMAMUXChannelBaseAndMask         0x08001121   Thumb Code    58  stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask)
    i.DMA_CalcDMAMUXRequestGenBaseAndMask    0x08001164   Section        0  stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask)
    DMA_CalcDMAMUXRequestGenBaseAndMask      0x08001165   Thumb Code    32  stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask)
    i.DMA_SetConfig                          0x08001188   Section        0  stm32g4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08001189   Thumb Code    64  stm32g4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x080011c8   Section        0  stm32g4xx_it.o(i.DebugMon_Handler)
    i.Encode_scan                            0x080011cc   Section        0  key.o(i.Encode_scan)
    i.Error_Handler                          0x08001238   Section        0  main.o(i.Error_Handler)
    i.Get_data_from_ROM                      0x08001240   Section        0  oled.o(i.Get_data_from_ROM)
    i.HAL_ADCEx_MultiModeConfigChannel       0x08001288   Section        0  stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel)
    i.HAL_ADC_ConfigChannel                  0x08001394   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x0800189c   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_ConvHalfCpltCallback           0x0800189e   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    i.HAL_ADC_ErrorCallback                  0x080018a0   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    i.HAL_ADC_GetState                       0x080018a2   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_GetState)
    i.HAL_ADC_Init                           0x080018a8   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_MspInit                        0x08001b04   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_Start_DMA                      0x08001bd0   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    i.HAL_ADC_Stop_DMA                       0x08001ce4   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_DMA)
    i.HAL_DMA_Abort                          0x08001d6c   Section        0  stm32g4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08001de2   Section        0  stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08001e76   Section        0  stm32g4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08001f74   Section        0  stm32g4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08002044   Section        0  stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08002108   Section        0  stm32g4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08002130   Section        0  stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x080022fc   Section        0  stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x0800230c   Section        0  stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08002318   Section        0  stm32g4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08002324   Section        0  stm32g4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x0800233c   Section        0  stm32g4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x0800235c   Section        0  stm32g4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x080023b4   Section        0  stm32g4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x080023f8   Section        0  stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08002420   Section        0  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x0800249c   Section        0  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_ControlVoltageScaling        0x080024c4   Section        0  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    i.HAL_PWREx_DisableUCPDDeadBattery       0x080025dc   Section        0  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDDeadBattery)
    i.HAL_RCCEx_PeriphCLKConfig              0x080025f0   Section        0  stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x08002960   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08002b84   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08002b90   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08002bb4   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08002bd8   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08002c70   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x080031cc   Section        0  stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_Break2Callback               0x08003200   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback)
    i.HAL_TIMEx_BreakCallback                0x08003202   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x08003204   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x08003208   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_DirectionChangeCallback      0x080032d0   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DirectionChangeCallback)
    i.HAL_TIMEx_EnableDeadTimePreload        0x080032d2   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableDeadTimePreload)
    i.HAL_TIMEx_EncoderIndexCallback         0x080032e4   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EncoderIndexCallback)
    i.HAL_TIMEx_IndexErrorCallback           0x080032e6   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_IndexErrorCallback)
    i.HAL_TIMEx_MasterConfigSynchronization  0x080032e8   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIMEx_PWMN_Start_IT                0x080033a0   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT)
    i.HAL_TIMEx_TransitionErrorCallback      0x080034e8   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_TransitionErrorCallback)
    i.HAL_TIM_Base_Init                      0x080034ea   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08003558   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start                     0x08003724   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    i.HAL_TIM_Base_Start_IT                  0x080037b4   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_ConfigClockSource              0x08003850   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_Encoder_Init                   0x08003984   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x08003a50   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_Encoder_Start                  0x08003ac8   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    i.HAL_TIM_IC_CaptureCallback             0x08003b94   Section        0  task.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IC_ConfigChannel               0x08003c3c   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel)
    i.HAL_TIM_IC_Init                        0x08003d1a   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init)
    i.HAL_TIM_IC_MspInit                     0x08003d88   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspInit)
    i.HAL_TIM_IC_Start_IT                    0x08003d8c   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT)
    i.HAL_TIM_IRQHandler                     0x08003f3c   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x0800415c   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08004228   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x0800422a   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x0800439a   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08004408   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x0800440a   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x0800440c   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PWM_Start_IT                   0x08004558   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT)
    i.HAL_TIM_PeriodElapsedCallback          0x080046f8   Section        0  task.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_ReadCapturedValue              0x080047a0   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue)
    i.HAL_TIM_TriggerCallback                0x080047d2   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_DisableFifoMode             0x080047d4   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode)
    i.HAL_UARTEx_RxEventCallback             0x08004822   Section        0  stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UARTEx_RxFifoFullCallback          0x08004824   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback)
    i.HAL_UARTEx_SetRxFifoThreshold          0x08004826   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold)
    i.HAL_UARTEx_SetTxFifoThreshold          0x08004884   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold)
    i.HAL_UARTEx_TxFifoEmptyCallback         0x080048e2   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback)
    i.HAL_UARTEx_WakeupCallback              0x080048e4   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    i.HAL_UART_ErrorCallback                 0x080048e6   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x080048e8   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08004cb8   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08004d30   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x08004dd4   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08004e30   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_Transmit                      0x08004e32   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08004ef4   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08004ef6   Section        0  stm32g4xx_it.o(i.HardFault_Handler)
    i.LL_ADC_Enable                          0x08004efc   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_Enable)
    LL_ADC_Enable                            0x08004efd   Thumb Code    12  stm32g4xx_hal_adc.o(i.LL_ADC_Enable)
    i.LL_ADC_GetMultimode                    0x08004f0c   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode)
    LL_ADC_GetMultimode                      0x08004f0d   Thumb Code    10  stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode)
    i.LL_ADC_GetOffsetChannel                0x08004f16   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_GetOffsetChannel)
    LL_ADC_GetOffsetChannel                  0x08004f17   Thumb Code    18  stm32g4xx_hal_adc.o(i.LL_ADC_GetOffsetChannel)
    i.LL_ADC_INJ_IsConversionOngoing         0x08004f28   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing)
    LL_ADC_INJ_IsConversionOngoing           0x08004f29   Thumb Code    10  stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing)
    i.LL_ADC_IsDisableOngoing                0x08004f32   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_IsDisableOngoing)
    LL_ADC_IsDisableOngoing                  0x08004f33   Thumb Code    10  stm32g4xx_hal_adc.o(i.LL_ADC_IsDisableOngoing)
    i.LL_ADC_IsEnabled                       0x08004f3c   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled)
    LL_ADC_IsEnabled                         0x08004f3d   Thumb Code    10  stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled)
    i.LL_ADC_IsEnabled                       0x08004f46   Section        0  stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled)
    LL_ADC_IsEnabled                         0x08004f47   Thumb Code    10  stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled)
    i.LL_ADC_IsInternalRegulatorEnabled      0x08004f50   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_IsInternalRegulatorEnabled)
    LL_ADC_IsInternalRegulatorEnabled        0x08004f51   Thumb Code    10  stm32g4xx_hal_adc.o(i.LL_ADC_IsInternalRegulatorEnabled)
    i.LL_ADC_REG_IsConversionOngoing         0x08004f5a   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing)
    LL_ADC_REG_IsConversionOngoing           0x08004f5b   Thumb Code    10  stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing)
    i.LL_ADC_REG_IsConversionOngoing         0x08004f64   Section        0  stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing)
    LL_ADC_REG_IsConversionOngoing           0x08004f65   Thumb Code    10  stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing)
    i.LL_ADC_REG_IsTriggerSourceSWStart      0x08004f6e   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart)
    LL_ADC_REG_IsTriggerSourceSWStart        0x08004f6f   Thumb Code    18  stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart)
    i.LL_ADC_REG_StartConversion             0x08004f80   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_REG_StartConversion)
    LL_ADC_REG_StartConversion               0x08004f81   Thumb Code    12  stm32g4xx_hal_adc.o(i.LL_ADC_REG_StartConversion)
    i.LL_ADC_SetChannelSamplingTime          0x08004f90   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_SetChannelSamplingTime)
    LL_ADC_SetChannelSamplingTime            0x08004f91   Thumb Code    40  stm32g4xx_hal_adc.o(i.LL_ADC_SetChannelSamplingTime)
    i.LL_ADC_SetCommonPathInternalCh         0x08004fb8   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_SetCommonPathInternalCh)
    LL_ADC_SetCommonPathInternalCh           0x08004fb9   Thumb Code    12  stm32g4xx_hal_adc.o(i.LL_ADC_SetCommonPathInternalCh)
    i.LL_ADC_SetOffsetState                  0x08004fc4   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_SetOffsetState)
    LL_ADC_SetOffsetState                    0x08004fc5   Thumb Code    22  stm32g4xx_hal_adc.o(i.LL_ADC_SetOffsetState)
    i.LL_ADC_SetSamplingTimeCommonConfig     0x08004fda   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_SetSamplingTimeCommonConfig)
    LL_ADC_SetSamplingTimeCommonConfig       0x08004fdb   Thumb Code    12  stm32g4xx_hal_adc.o(i.LL_ADC_SetSamplingTimeCommonConfig)
    i.MX_ADC1_Init                           0x08004fe8   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_DMA_Init                            0x080050dc   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x0800512c   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_TIM15_Init                          0x08005268   Section        0  tim.o(i.MX_TIM15_Init)
    i.MX_TIM1_Init                           0x0800530c   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x08005428   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x080054c8   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM4_Init                           0x08005538   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_TIM6_Init                           0x080055b4   Section        0  tim.o(i.MX_TIM6_Init)
    i.MX_TIM7_Init                           0x08005600   Section        0  tim.o(i.MX_TIM7_Init)
    i.MX_USART1_UART_Init                    0x0800564c   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MemManage_Handler                      0x080056b8   Section        0  stm32g4xx_it.o(i.MemManage_Handler)
    i.Menu                                   0x080056bc   Section        0  menu.o(i.Menu)
    i.Menu_Init                              0x08005910   Section        0  menu.o(i.Menu_Init)
    i.NMI_Handler                            0x08005974   Section        0  stm32g4xx_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x08005978   Section        0  oled.o(i.OLED_Clear)
    i.OLED_ColorTurn                         0x080059b8   Section        0  oled.o(i.OLED_ColorTurn)
    i.OLED_DisplayTurn                       0x080059d6   Section        0  oled.o(i.OLED_DisplayTurn)
    i.OLED_Display_16x16                     0x08005a04   Section        0  oled.o(i.OLED_Display_16x16)
    i.OLED_Display_8x16                      0x08005a56   Section        0  oled.o(i.OLED_Display_8x16)
    i.OLED_Display_GB2312_string             0x08005aa8   Section        0  oled.o(i.OLED_Display_GB2312_string)
    i.OLED_Init                              0x08005c28   Section        0  oled.o(i.OLED_Init)
    i.OLED_ShowNum                           0x08005d08   Section        0  oled.o(i.OLED_ShowNum)
    i.OLED_WR_Byte                           0x08005e48   Section        0  oled.o(i.OLED_WR_Byte)
    i.OLED_address                           0x08005ed4   Section        0  oled.o(i.OLED_address)
    i.OLED_get_data_from_ROM                 0x08005f04   Section        0  oled.o(i.OLED_get_data_from_ROM)
    i.PID_Control_Current_Process            0x08005f5c   Section        0  pid_control.o(i.PID_Control_Current_Process)
    PID_Control_Current_Process              0x08005f5d   Thumb Code   382  pid_control.o(i.PID_Control_Current_Process)
    i.PID_Control_Disable_All                0x08006180   Section        0  pid_control.o(i.PID_Control_Disable_All)
    i.PID_Control_Enable_Current             0x0800618c   Section        0  pid_control.o(i.PID_Control_Enable_Current)
    i.PID_Control_Enable_Voltage             0x08006200   Section        0  pid_control.o(i.PID_Control_Enable_Voltage)
    i.PID_Control_Init                       0x08006268   Section        0  pid_control.o(i.PID_Control_Init)
    i.PID_Control_Loop                       0x080062e8   Section        0  pid_control.o(i.PID_Control_Loop)
    i.PID_Control_Set_Mode                   0x08006314   Section        0  pid_control.o(i.PID_Control_Set_Mode)
    i.PID_Control_Voltage_Process            0x08006414   Section        0  pid_control.o(i.PID_Control_Voltage_Process)
    PID_Control_Voltage_Process              0x08006415   Thumb Code   440  pid_control.o(i.PID_Control_Voltage_Process)
    i.PID_Current_Control                    0x08006688   Section        0  pid_yyds.o(i.PID_Current_Control)
    i.PID_Debug_Info                         0x080067a4   Section        0  pid_yyds.o(i.PID_Debug_Info)
    i.PID_Init                               0x080069d8   Section        0  pid_yyds.o(i.PID_Init)
    i.PID_Reset                              0x08006a1c   Section        0  pid_yyds.o(i.PID_Reset)
    i.PID_SPWM_Depth_Control                 0x08006a50   Section        0  pid_yyds.o(i.PID_SPWM_Depth_Control)
    i.PendSV_Handler                         0x08006b6c   Section        0  stm32g4xx_it.o(i.PendSV_Handler)
    i.RCC_GetSysClockFreqFromPLLSource       0x08006b70   Section        0  stm32g4xx_hal_rcc.o(i.RCC_GetSysClockFreqFromPLLSource)
    RCC_GetSysClockFreqFromPLLSource         0x08006b71   Thumb Code    90  stm32g4xx_hal_rcc.o(i.RCC_GetSysClockFreqFromPLLSource)
    i.SVC_Handler                            0x08006bd8   Section        0  stm32g4xx_it.o(i.SVC_Handler)
    i.Send_Command_to_ROM                    0x08006bdc   Section        0  oled.o(i.Send_Command_to_ROM)
    i.SysTick_Handler                        0x08006c2c   Section        0  stm32g4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08006c34   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08006c9c   Section        0  system_stm32g4xx.o(i.SystemInit)
    i.TIM1_BRK_TIM15_IRQHandler              0x08006cb0   Section        0  stm32g4xx_it.o(i.TIM1_BRK_TIM15_IRQHandler)
    i.TIM2_IRQHandler                        0x08006cc8   Section        0  stm32g4xx_it.o(i.TIM2_IRQHandler)
    i.TIM6_DAC_IRQHandler                    0x08006cd8   Section        0  stm32g4xx_it.o(i.TIM6_DAC_IRQHandler)
    i.TIM7_IRQHandler                        0x08006ce8   Section        0  stm32g4xx_it.o(i.TIM7_IRQHandler)
    i.TIM_Base_SetConfig                     0x08006cf8   Section        0  stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x08006dc4   Section        0  stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_CCxNChannelCmd                     0x08006de6   Section        0  stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd)
    TIM_CCxNChannelCmd                       0x08006de7   Thumb Code    34  stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd)
    i.TIM_ETR_SetConfig                      0x08006e08   Section        0  stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08006e20   Section        0  stm32g4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08006e21   Thumb Code    18  stm32g4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x08006e38   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08006e39   Thumb Code   146  stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08006ee4   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08006f98   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08006f99   Thumb Code   154  stm32g4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x0800704c   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x0800704d   Thumb Code   156  stm32g4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_OC5_SetConfig                      0x08007100   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC5_SetConfig)
    TIM_OC5_SetConfig                        0x08007101   Thumb Code    86  stm32g4xx_hal_tim.o(i.TIM_OC5_SetConfig)
    i.TIM_OC6_SetConfig                      0x08007170   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC6_SetConfig)
    TIM_OC6_SetConfig                        0x08007171   Thumb Code    88  stm32g4xx_hal_tim.o(i.TIM_OC6_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x080071e0   Section        0  stm32g4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x080071e1   Thumb Code    38  stm32g4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI1_SetConfig                      0x08007208   Section        0  stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig)
    i.TIM_TI2_ConfigInputStage               0x08007284   Section        0  stm32g4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08007285   Thumb Code    40  stm32g4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.TIM_TI2_SetConfig                      0x080072ac   Section        0  stm32g4xx_hal_tim.o(i.TIM_TI2_SetConfig)
    TIM_TI2_SetConfig                        0x080072ad   Thumb Code    58  stm32g4xx_hal_tim.o(i.TIM_TI2_SetConfig)
    i.TIM_TI3_SetConfig                      0x080072e6   Section        0  stm32g4xx_hal_tim.o(i.TIM_TI3_SetConfig)
    TIM_TI3_SetConfig                        0x080072e7   Thumb Code    56  stm32g4xx_hal_tim.o(i.TIM_TI3_SetConfig)
    i.TIM_TI4_SetConfig                      0x0800731e   Section        0  stm32g4xx_hal_tim.o(i.TIM_TI4_SetConfig)
    TIM_TI4_SetConfig                        0x0800731f   Thumb Code    60  stm32g4xx_hal_tim.o(i.TIM_TI4_SetConfig)
    i.UARTEx_SetNbDataToProcess              0x0800735c   Section        0  stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess)
    UARTEx_SetNbDataToProcess                0x0800735d   Thumb Code    78  stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess)
    i.UART_AdvFeatureConfig                  0x080073b4   Section        0  stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig)
    i.UART_CheckIdleState                    0x080074ac   Section        0  stm32g4xx_hal_uart.o(i.UART_CheckIdleState)
    i.UART_DMAAbortOnError                   0x08007596   Section        0  stm32g4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08007597   Thumb Code    20  stm32g4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x080075ac   Section        0  stm32g4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x080075ad   Thumb Code   104  stm32g4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x08007618   Section        0  stm32g4xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x08007619   Thumb Code    48  stm32g4xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_RxISR_16BIT                       0x08007648   Section        0  stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT)
    UART_RxISR_16BIT                         0x08007649   Thumb Code   256  stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT)
    i.UART_RxISR_16BIT_FIFOEN                0x0800774c   Section        0  stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN)
    UART_RxISR_16BIT_FIFOEN                  0x0800774d   Thumb Code   498  stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN)
    i.UART_RxISR_8BIT                        0x0800794c   Section        0  stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT)
    UART_RxISR_8BIT                          0x0800794d   Thumb Code   254  stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT)
    i.UART_RxISR_8BIT_FIFOEN                 0x08007a50   Section        0  stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN)
    UART_RxISR_8BIT_FIFOEN                   0x08007a51   Thumb Code   496  stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN)
    i.UART_SetConfig                         0x08007c4c   Section        0  stm32g4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_IT                  0x08007fd0   Section        0  stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_WaitOnFlagUntilTimeout            0x0800812c   Section        0  stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x080081d8   Section        0  stm32g4xx_it.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x080081e8   Section        0  stm32g4xx_it.o(i.UsageFault_Handler)
    i.__0printf                              0x080081ec   Section        0  printfa.o(i.__0printf)
    i.__NVIC_GetPriorityGrouping             0x0800820c   Section        0  stm32g4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    __NVIC_GetPriorityGrouping               0x0800820d   Thumb Code    10  stm32g4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    i.__NVIC_SetPriority                     0x0800821c   Section        0  stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x0800821d   Thumb Code    32  stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__scatterload_copy                     0x08008244   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08008252   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08008254   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x08008264   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08008265   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x080083e8   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x080083e9   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08008a9c   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08008a9d   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08008ac0   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08008ac1   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i.adc_multi_channel_sample               0x08008af0   Section        0  my_adc.o(i.adc_multi_channel_sample)
    adc_multi_channel_sample                 0x08008af1   Thumb Code    74  my_adc.o(i.adc_multi_channel_sample)
    i.calculate_average                      0x08008b40   Section        0  my_adc.o(i.calculate_average)
    calculate_average                        0x08008b41   Thumb Code    34  my_adc.o(i.calculate_average)
    i.calculate_dc_current                   0x08008b62   Section        0  my_adc.o(i.calculate_dc_current)
    i.calculate_dc_voltage                   0x08008b76   Section        0  my_adc.o(i.calculate_dc_voltage)
    i.calculate_rms_current                  0x08008b8a   Section        0  my_adc.o(i.calculate_rms_current)
    i.calculate_rms_voltage                  0x08008b9e   Section        0  my_adc.o(i.calculate_rms_voltage)
    i.delay                                  0x08008bb2   Section        0  oled.o(i.delay)
    delay                                    0x08008bb3   Thumb Code    16  oled.o(i.delay)
    i.display                                0x08008bc4   Section        0  menu.o(i.display)
    i.display_basic_info                     0x08008c40   Section        0  task.o(i.display_basic_info)
    display_basic_info                       0x08008c41   Thumb Code    64  task.o(i.display_basic_info)
    i.display_dc_basic_info                  0x08008c98   Section        0  task.o(i.display_dc_basic_info)
    display_dc_basic_info                    0x08008c99   Thumb Code    64  task.o(i.display_dc_basic_info)
    i.display_dc_pid_status                  0x08008cf0   Section        0  task.o(i.display_dc_pid_status)
    display_dc_pid_status                    0x08008cf1   Thumb Code   130  task.o(i.display_dc_pid_status)
    i.fputc                                  0x08008da0   Section        0  usart.o(i.fputc)
    i.handle_menu_return                     0x08008dbc   Section        0  task.o(i.handle_menu_return)
    handle_menu_return                       0x08008dbd   Thumb Code    48  task.o(i.handle_menu_return)
    i.key_scan                               0x08008dfc   Section        0  key.o(i.key_scan)
    i.main                                   0x08008ec0   Section        0  main.o(i.main)
    i.sliding_average_filter                 0x08009068   Section        0  my_adc.o(i.sliding_average_filter)
    sliding_average_filter                   0x08009069   Thumb Code    52  my_adc.o(i.sliding_average_filter)
    i.sliding_average_filter_current         0x0800909c   Section        0  my_adc.o(i.sliding_average_filter_current)
    i.sliding_average_filter_dc_current      0x080090b4   Section        0  my_adc.o(i.sliding_average_filter_dc_current)
    i.sliding_average_filter_dc_voltage      0x080090cc   Section        0  my_adc.o(i.sliding_average_filter_dc_voltage)
    i.sliding_average_filter_voltage         0x080090e4   Section        0  my_adc.o(i.sliding_average_filter_voltage)
    i.task1                                  0x080090fc   Section        0  task.o(i.task1)
    i.task2                                  0x08009150   Section        0  task.o(i.task2)
    i.task3                                  0x080091cc   Section        0  task.o(i.task3)
    i.task_init                              0x080092a4   Section        0  task.o(i.task_init)
    .constdata                               0x080092dc   Section       24  stm32g4xx_hal_uart.o(.constdata)
    .constdata                               0x080092f4   Section       16  stm32g4xx_hal_uart_ex.o(.constdata)
    numerator                                0x080092f4   Data           8  stm32g4xx_hal_uart_ex.o(.constdata)
    denominator                              0x080092fc   Data           8  stm32g4xx_hal_uart_ex.o(.constdata)
    .constdata                               0x08009304   Section       24  system_stm32g4xx.o(.constdata)
    .constdata                               0x0800931c   Section     2400  task.o(.constdata)
    sin_value                                0x0800931c   Data        2400  task.o(.constdata)
    .conststring                             0x08009c7c   Section       86  menu.o(.conststring)
    .data                                    0x20000000   Section        1  usart.o(.data)
    .data                                    0x20000004   Section       12  stm32g4xx_hal.o(.data)
    .data                                    0x20000010   Section        4  system_stm32g4xx.o(.data)
    .data                                    0x20000014   Section        2  key.o(.data)
    key_up                                   0x20000014   Data           2  key.o(.data)
    .data                                    0x20000018   Section      256  menu.o(.data)
    .data                                    0x20000118   Section       52  my_adc.o(.data)
    ac_voltage_index                         0x20000148   Data           1  my_adc.o(.data)
    ac_current_index                         0x20000149   Data           1  my_adc.o(.data)
    dc_voltage_index                         0x2000014a   Data           1  my_adc.o(.data)
    dc_current_index                         0x2000014b   Data           1  my_adc.o(.data)
    .data                                    0x2000014c   Section        4  oled.o(.data)
    .data                                    0x20000150   Section       36  task.o(.data)
    IC1Value                                 0x20000168   Data           4  task.o(.data)
    IC2Value                                 0x2000016c   Data           4  task.o(.data)
    CaptureNumber                            0x20000170   Data           4  task.o(.data)
    .data                                    0x20000174   Section       28  pid_control.o(.data)
    last_update_time                         0x2000018c   Data           4  pid_control.o(.data)
    .data                                    0x20000190   Section        4  stdout.o(.data)
    .bss                                     0x20000194   Section      204  adc.o(.bss)
    .bss                                     0x20000260   Section      532  tim.o(.bss)
    .bss                                     0x20000474   Section      248  usart.o(.bss)
    .bss                                     0x2000056c   Section      360  my_adc.o(.bss)
    adc_buffer                               0x2000060c   Data         200  my_adc.o(.bss)
    .bss                                     0x200006d4   Section      200  pid_yyds.o(.bss)
    .bss                                     0x2000079c   Section      120  pid_control.o(.bss)
    STACK                                    0x20000818   Section     1024  startup_stm32g431xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001d8   Number         0  startup_stm32g431xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32g431xx.o(RESET)
    __Vectors_End                            0x080001d8   Data           0  startup_stm32g431xx.o(RESET)
    __main                                   0x080001d9   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001d9   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001dd   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001e1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001e1   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001e1   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001e1   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x080001e9   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x080001ed   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x080001ed   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080001f1   Thumb Code     8  startup_stm32g431xx.o(.text)
    ADC1_2_IRQHandler                        0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    COMP1_2_3_IRQHandler                     0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    COMP4_IRQHandler                         0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    CORDIC_IRQHandler                        0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    CRS_IRQHandler                           0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel4_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel5_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel6_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMAMUX_OVR_IRQHandler                    0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI0_IRQHandler                         0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI15_10_IRQHandler                     0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI1_IRQHandler                         0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI2_IRQHandler                         0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI3_IRQHandler                         0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI4_IRQHandler                         0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI9_5_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    FDCAN1_IT0_IRQHandler                    0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    FDCAN1_IT1_IRQHandler                    0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    FLASH_IRQHandler                         0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    FMAC_IRQHandler                          0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    FPU_IRQHandler                           0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C1_ER_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C1_EV_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C2_ER_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C2_EV_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C3_ER_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C3_EV_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    LPTIM1_IRQHandler                        0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    LPUART1_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    PVD_PVM_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    RCC_IRQHandler                           0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    RNG_IRQHandler                           0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    RTC_TAMP_LSECSS_IRQHandler               0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    SAI1_IRQHandler                          0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    SPI1_IRQHandler                          0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    SPI2_IRQHandler                          0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    SPI3_IRQHandler                          0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM1_CC_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM1_TRG_COM_TIM17_IRQHandler            0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM1_UP_TIM16_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM3_IRQHandler                          0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM4_IRQHandler                          0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_BRK_IRQHandler                      0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_CC_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_UP_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    UART4_IRQHandler                         0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    UCPD1_IRQHandler                         0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    USART2_IRQHandler                        0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    USART3_IRQHandler                        0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    USBWakeUp_IRQHandler                     0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    USB_HP_IRQHandler                        0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    USB_LP_IRQHandler                        0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    WWDG_IRQHandler                          0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    __aeabi_uldivmod                         0x08000215   Thumb Code    98  uldiv.o(.text)
    __aeabi_memset                           0x08000277   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000277   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000277   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000285   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000285   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000285   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000289   Thumb Code    18  memseta.o(.text)
    strcmp                                   0x0800029b   Thumb Code    28  strcmp.o(.text)
    __aeabi_f2d                              0x080002b7   Thumb Code    38  f2d.o(.text)
    __aeabi_uidiv                            0x080002dd   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080002dd   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x08000309   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000309   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000327   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000327   Thumb Code     0  llushr.o(.text)
    __I$use$fp                               0x08000347   Thumb Code     0  iusefp.o(.text)
    __aeabi_dadd                             0x08000347   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000489   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x0800048f   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x08000495   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000579   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x08000657   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000689   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x080006b9   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080006b9   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x080006dd   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080006dd   Thumb Code     0  llsshr.o(.text)
    _double_round                            0x08000701   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x0800071f   Thumb Code   156  depilogue.o(.text)
    __decompress                             0x080007bb   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x080007bb   Thumb Code    86  __dczerorl2.o(.text)
    ADC_ConversionStop                       0x08000811   Thumb Code   276  stm32g4xx_hal_adc.o(i.ADC_ConversionStop)
    ADC_DMAConvCplt                          0x0800092d   Thumb Code   146  stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt)
    ADC_DMAError                             0x080009bf   Thumb Code    30  stm32g4xx_hal_adc.o(i.ADC_DMAError)
    ADC_DMAHalfConvCplt                      0x080009dd   Thumb Code    14  stm32g4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    ADC_Disable                              0x080009ed   Thumb Code   142  stm32g4xx_hal_adc.o(i.ADC_Disable)
    ADC_Enable                               0x08000a81   Thumb Code   182  stm32g4xx_hal_adc.o(i.ADC_Enable)
    BusFault_Handler                         0x08000b49   Thumb Code     4  stm32g4xx_it.o(i.BusFault_Handler)
    Calculate_ADC                            0x08000b4d   Thumb Code   178  my_adc.o(i.Calculate_ADC)
    Calculate_DC_ADC                         0x08000c3d   Thumb Code   170  my_adc.o(i.Calculate_DC_ADC)
    DC_Disable_PID_Control                   0x08000d31   Thumb Code    24  pid_control.o(i.DC_Disable_PID_Control)
    DC_Enable_Constant_Current               0x08000d6d   Thumb Code    68  pid_control.o(i.DC_Enable_Constant_Current)
    DC_Enable_Constant_Voltage               0x08000de5   Thumb Code    66  pid_control.o(i.DC_Enable_Constant_Voltage)
    DC_Get_PID_Status                        0x08000e5d   Thumb Code    94  pid_control.o(i.DC_Get_PID_Status)
    DC_PID_Control_Loop                      0x0800100d   Thumb Code   132  pid_control.o(i.DC_PID_Control_Loop)
    DC_Set_PWM_Output                        0x080010f1   Thumb Code    22  pid_control.o(i.DC_Set_PWM_Output)
    DMA1_Channel1_IRQHandler                 0x08001111   Thumb Code    10  stm32g4xx_it.o(i.DMA1_Channel1_IRQHandler)
    DebugMon_Handler                         0x080011c9   Thumb Code     2  stm32g4xx_it.o(i.DebugMon_Handler)
    Encode_scan                              0x080011cd   Thumb Code   104  key.o(i.Encode_scan)
    Error_Handler                            0x08001239   Thumb Code     6  main.o(i.Error_Handler)
    Get_data_from_ROM                        0x08001241   Thumb Code    62  oled.o(i.Get_data_from_ROM)
    HAL_ADCEx_MultiModeConfigChannel         0x08001289   Thumb Code   260  stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel)
    HAL_ADC_ConfigChannel                    0x08001395   Thumb Code  1252  stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x0800189d   Thumb Code     2  stm32g4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ConvHalfCpltCallback             0x0800189f   Thumb Code     2  stm32g4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    HAL_ADC_ErrorCallback                    0x080018a1   Thumb Code     2  stm32g4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    HAL_ADC_GetState                         0x080018a3   Thumb Code     6  stm32g4xx_hal_adc.o(i.HAL_ADC_GetState)
    HAL_ADC_Init                             0x080018a9   Thumb Code   576  stm32g4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x08001b05   Thumb Code   190  adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_Start_DMA                        0x08001bd1   Thumb Code   254  stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    HAL_ADC_Stop_DMA                         0x08001ce5   Thumb Code   136  stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_DMA)
    HAL_DMA_Abort                            0x08001d6d   Thumb Code   118  stm32g4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08001de3   Thumb Code   148  stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08001e77   Thumb Code   254  stm32g4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08001f75   Thumb Code   200  stm32g4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08002045   Thumb Code   194  stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08002109   Thumb Code    36  stm32g4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08002131   Thumb Code   428  stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x080022fd   Thumb Code    16  stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x0800230d   Thumb Code    10  stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08002319   Thumb Code     6  stm32g4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08002325   Thumb Code    16  stm32g4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x0800233d   Thumb Code    30  stm32g4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x0800235d   Thumb Code    74  stm32g4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x080023b5   Thumb Code    62  stm32g4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x080023f9   Thumb Code    40  stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08002421   Thumb Code   122  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x0800249d   Thumb Code    32  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_ControlVoltageScaling          0x080024c5   Thumb Code   268  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    HAL_PWREx_DisableUCPDDeadBattery         0x080025dd   Thumb Code    14  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDDeadBattery)
    HAL_RCCEx_PeriphCLKConfig                0x080025f1   Thumb Code   872  stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x08002961   Thumb Code   522  stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08002b85   Thumb Code     6  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08002b91   Thumb Code    26  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08002bb5   Thumb Code    26  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08002bd9   Thumb Code   138  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08002c71   Thumb Code  1360  stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x080031cd   Thumb Code    52  stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_Break2Callback                 0x08003201   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback)
    HAL_TIMEx_BreakCallback                  0x08003203   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08003205   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_ConfigBreakDeadTime            0x08003209   Thumb Code   192  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_DirectionChangeCallback        0x080032d1   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DirectionChangeCallback)
    HAL_TIMEx_EnableDeadTimePreload          0x080032d3   Thumb Code    18  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableDeadTimePreload)
    HAL_TIMEx_EncoderIndexCallback           0x080032e5   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EncoderIndexCallback)
    HAL_TIMEx_IndexErrorCallback             0x080032e7   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_IndexErrorCallback)
    HAL_TIMEx_MasterConfigSynchronization    0x080032e9   Thumb Code   158  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIMEx_PWMN_Start_IT                  0x080033a1   Thumb Code   304  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT)
    HAL_TIMEx_TransitionErrorCallback        0x080034e9   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_TransitionErrorCallback)
    HAL_TIM_Base_Init                        0x080034eb   Thumb Code   110  stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08003559   Thumb Code   430  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start                       0x08003725   Thumb Code   118  stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    HAL_TIM_Base_Start_IT                    0x080037b5   Thumb Code   130  stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_ConfigClockSource                0x08003851   Thumb Code   300  stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x08003985   Thumb Code   198  stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08003a51   Thumb Code   110  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_Encoder_Start                    0x08003ac9   Thumb Code   204  stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    HAL_TIM_IC_CaptureCallback               0x08003b95   Thumb Code   136  task.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IC_ConfigChannel                 0x08003c3d   Thumb Code   222  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel)
    HAL_TIM_IC_Init                          0x08003d1b   Thumb Code   110  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init)
    HAL_TIM_IC_MspInit                       0x08003d89   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspInit)
    HAL_TIM_IC_Start_IT                      0x08003d8d   Thumb Code   408  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT)
    HAL_TIM_IRQHandler                       0x08003f3d   Thumb Code   544  stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x0800415d   Thumb Code   188  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_DelayElapsedCallback          0x08004229   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x0800422b   Thumb Code   368  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x0800439b   Thumb Code   110  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08004409   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x0800440b   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x0800440d   Thumb Code   298  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PWM_Start_IT                     0x08004559   Thumb Code   384  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT)
    HAL_TIM_PeriodElapsedCallback            0x080046f9   Thumb Code   134  task.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_ReadCapturedValue                0x080047a1   Thumb Code    50  stm32g4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue)
    HAL_TIM_TriggerCallback                  0x080047d3   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_DisableFifoMode               0x080047d5   Thumb Code    78  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode)
    HAL_UARTEx_RxEventCallback               0x08004823   Thumb Code     2  stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UARTEx_RxFifoFullCallback            0x08004825   Thumb Code     2  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback)
    HAL_UARTEx_SetRxFifoThreshold            0x08004827   Thumb Code    94  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold)
    HAL_UARTEx_SetTxFifoThreshold            0x08004885   Thumb Code    94  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold)
    HAL_UARTEx_TxFifoEmptyCallback           0x080048e3   Thumb Code     2  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback)
    HAL_UARTEx_WakeupCallback                0x080048e5   Thumb Code     2  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    HAL_UART_ErrorCallback                   0x080048e7   Thumb Code     2  stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x080048e9   Thumb Code   962  stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08004cb9   Thumb Code   120  stm32g4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08004d31   Thumb Code   154  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x08004dd5   Thumb Code    88  stm32g4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08004e31   Thumb Code     2  stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_Transmit                        0x08004e33   Thumb Code   194  stm32g4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08004ef5   Thumb Code     2  stm32g4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08004ef7   Thumb Code     4  stm32g4xx_it.o(i.HardFault_Handler)
    MX_ADC1_Init                             0x08004fe9   Thumb Code   222  adc.o(i.MX_ADC1_Init)
    MX_DMA_Init                              0x080050dd   Thumb Code    74  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x0800512d   Thumb Code   300  gpio.o(i.MX_GPIO_Init)
    MX_TIM15_Init                            0x08005269   Thumb Code   156  tim.o(i.MX_TIM15_Init)
    MX_TIM1_Init                             0x0800530d   Thumb Code   276  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x08005429   Thumb Code   156  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x080054c9   Thumb Code   104  tim.o(i.MX_TIM3_Init)
    MX_TIM4_Init                             0x08005539   Thumb Code   116  tim.o(i.MX_TIM4_Init)
    MX_TIM6_Init                             0x080055b5   Thumb Code    68  tim.o(i.MX_TIM6_Init)
    MX_TIM7_Init                             0x08005601   Thumb Code    68  tim.o(i.MX_TIM7_Init)
    MX_USART1_UART_Init                      0x0800564d   Thumb Code    98  usart.o(i.MX_USART1_UART_Init)
    MemManage_Handler                        0x080056b9   Thumb Code     4  stm32g4xx_it.o(i.MemManage_Handler)
    Menu                                     0x080056bd   Thumb Code   554  menu.o(i.Menu)
    Menu_Init                                0x08005911   Thumb Code    72  menu.o(i.Menu_Init)
    NMI_Handler                              0x08005975   Thumb Code     4  stm32g4xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x08005979   Thumb Code    64  oled.o(i.OLED_Clear)
    OLED_ColorTurn                           0x080059b9   Thumb Code    30  oled.o(i.OLED_ColorTurn)
    OLED_DisplayTurn                         0x080059d7   Thumb Code    46  oled.o(i.OLED_DisplayTurn)
    OLED_Display_16x16                       0x08005a05   Thumb Code    82  oled.o(i.OLED_Display_16x16)
    OLED_Display_8x16                        0x08005a57   Thumb Code    82  oled.o(i.OLED_Display_8x16)
    OLED_Display_GB2312_string               0x08005aa9   Thumb Code   376  oled.o(i.OLED_Display_GB2312_string)
    OLED_Init                                0x08005c29   Thumb Code   222  oled.o(i.OLED_Init)
    OLED_ShowNum                             0x08005d09   Thumb Code   270  oled.o(i.OLED_ShowNum)
    OLED_WR_Byte                             0x08005e49   Thumb Code   134  oled.o(i.OLED_WR_Byte)
    OLED_address                             0x08005ed5   Thumb Code    46  oled.o(i.OLED_address)
    OLED_get_data_from_ROM                   0x08005f05   Thumb Code    84  oled.o(i.OLED_get_data_from_ROM)
    PID_Control_Disable_All                  0x08006181   Thumb Code    10  pid_control.o(i.PID_Control_Disable_All)
    PID_Control_Enable_Current               0x0800618d   Thumb Code    62  pid_control.o(i.PID_Control_Enable_Current)
    PID_Control_Enable_Voltage               0x08006201   Thumb Code    56  pid_control.o(i.PID_Control_Enable_Voltage)
    PID_Control_Init                         0x08006269   Thumb Code    74  pid_control.o(i.PID_Control_Init)
    PID_Control_Loop                         0x080062e9   Thumb Code    38  pid_control.o(i.PID_Control_Loop)
    PID_Control_Set_Mode                     0x08006315   Thumb Code   136  pid_control.o(i.PID_Control_Set_Mode)
    PID_Current_Control                      0x08006689   Thumb Code   270  pid_yyds.o(i.PID_Current_Control)
    PID_Debug_Info                           0x080067a5   Thumb Code   330  pid_yyds.o(i.PID_Debug_Info)
    PID_Init                                 0x080069d9   Thumb Code    64  pid_yyds.o(i.PID_Init)
    PID_Reset                                0x08006a1d   Thumb Code    48  pid_yyds.o(i.PID_Reset)
    PID_SPWM_Depth_Control                   0x08006a51   Thumb Code   270  pid_yyds.o(i.PID_SPWM_Depth_Control)
    PendSV_Handler                           0x08006b6d   Thumb Code     2  stm32g4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08006bd9   Thumb Code     2  stm32g4xx_it.o(i.SVC_Handler)
    Send_Command_to_ROM                      0x08006bdd   Thumb Code    74  oled.o(i.Send_Command_to_ROM)
    SysTick_Handler                          0x08006c2d   Thumb Code     8  stm32g4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08006c35   Thumb Code   104  main.o(i.SystemClock_Config)
    SystemInit                               0x08006c9d   Thumb Code    14  system_stm32g4xx.o(i.SystemInit)
    TIM1_BRK_TIM15_IRQHandler                0x08006cb1   Thumb Code    16  stm32g4xx_it.o(i.TIM1_BRK_TIM15_IRQHandler)
    TIM2_IRQHandler                          0x08006cc9   Thumb Code    10  stm32g4xx_it.o(i.TIM2_IRQHandler)
    TIM6_DAC_IRQHandler                      0x08006cd9   Thumb Code    10  stm32g4xx_it.o(i.TIM6_DAC_IRQHandler)
    TIM7_IRQHandler                          0x08006ce9   Thumb Code    10  stm32g4xx_it.o(i.TIM7_IRQHandler)
    TIM_Base_SetConfig                       0x08006cf9   Thumb Code   174  stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x08006dc5   Thumb Code    34  stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_ETR_SetConfig                        0x08006e09   Thumb Code    22  stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x08006ee5   Thumb Code   156  stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    TIM_TI1_SetConfig                        0x08007209   Thumb Code   102  stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig)
    UART_AdvFeatureConfig                    0x080073b5   Thumb Code   248  stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig)
    UART_CheckIdleState                      0x080074ad   Thumb Code   234  stm32g4xx_hal_uart.o(i.UART_CheckIdleState)
    UART_SetConfig                           0x08007c4d   Thumb Code   858  stm32g4xx_hal_uart.o(i.UART_SetConfig)
    UART_Start_Receive_IT                    0x08007fd1   Thumb Code   332  stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT)
    UART_WaitOnFlagUntilTimeout              0x0800812d   Thumb Code   172  stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    USART1_IRQHandler                        0x080081d9   Thumb Code    10  stm32g4xx_it.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x080081e9   Thumb Code     4  stm32g4xx_it.o(i.UsageFault_Handler)
    __0printf                                0x080081ed   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x080081ed   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x080081ed   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x080081ed   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x080081ed   Thumb Code     0  printfa.o(i.__0printf)
    __scatterload_copy                       0x08008245   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08008253   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08008255   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    calculate_dc_current                     0x08008b63   Thumb Code    20  my_adc.o(i.calculate_dc_current)
    calculate_dc_voltage                     0x08008b77   Thumb Code    20  my_adc.o(i.calculate_dc_voltage)
    calculate_rms_current                    0x08008b8b   Thumb Code    20  my_adc.o(i.calculate_rms_current)
    calculate_rms_voltage                    0x08008b9f   Thumb Code    20  my_adc.o(i.calculate_rms_voltage)
    display                                  0x08008bc5   Thumb Code   112  menu.o(i.display)
    fputc                                    0x08008da1   Thumb Code    22  usart.o(i.fputc)
    key_scan                                 0x08008dfd   Thumb Code   184  key.o(i.key_scan)
    main                                     0x08008ec1   Thumb Code   256  main.o(i.main)
    sliding_average_filter_current           0x0800909d   Thumb Code    18  my_adc.o(i.sliding_average_filter_current)
    sliding_average_filter_dc_current        0x080090b5   Thumb Code    18  my_adc.o(i.sliding_average_filter_dc_current)
    sliding_average_filter_dc_voltage        0x080090cd   Thumb Code    18  my_adc.o(i.sliding_average_filter_dc_voltage)
    sliding_average_filter_voltage           0x080090e5   Thumb Code    18  my_adc.o(i.sliding_average_filter_voltage)
    task1                                    0x080090fd   Thumb Code    78  task.o(i.task1)
    task2                                    0x08009151   Thumb Code    92  task.o(i.task2)
    task3                                    0x080091cd   Thumb Code   156  task.o(i.task3)
    task_init                                0x080092a5   Thumb Code    40  task.o(i.task_init)
    UARTPrescTable                           0x080092dc   Data          24  stm32g4xx_hal_uart.o(.constdata)
    AHBPrescTable                            0x08009304   Data          16  system_stm32g4xx.o(.constdata)
    APBPrescTable                            0x08009314   Data           8  system_stm32g4xx.o(.constdata)
    Region$$Table$$Base                      0x08009cd4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08009cf4   Number         0  anon$$obj.o(Region$$Table)
    usart_buff                               0x20000000   Data           1  usart.o(.data)
    uwTick                                   0x20000004   Data           4  stm32g4xx_hal.o(.data)
    uwTickPrio                               0x20000008   Data           4  stm32g4xx_hal.o(.data)
    uwTickFreq                               0x2000000c   Data           4  stm32g4xx_hal.o(.data)
    SystemCoreClock                          0x20000010   Data           4  system_stm32g4xx.o(.data)
    MenuPoint                                0x20000018   Data           4  menu.o(.data)
    SelectItem_Current                       0x2000001c   Data           2  menu.o(.data)
    SelectItem_Hidden                        0x2000001e   Data           2  menu.o(.data)
    SelectItem                               0x20000020   Data           2  menu.o(.data)
    Encoder_Key                              0x20000022   Data           1  menu.o(.data)
    Key                                      0x20000023   Data           1  menu.o(.data)
    key_flag                                 0x20000024   Data           1  menu.o(.data)
    MainMenu                                 0x20000028   Data          40  menu.o(.data)
    Menu0Second                              0x20000050   Data          80  menu.o(.data)
    Menutask1                                0x200000a0   Data          40  menu.o(.data)
    Menutask2                                0x200000c8   Data          40  menu.o(.data)
    Menutask3                                0x200000f0   Data          40  menu.o(.data)
    ac_voltage_raw                           0x20000118   Data           4  my_adc.o(.data)
    ac_current_raw                           0x2000011c   Data           4  my_adc.o(.data)
    AC_voltage_out                           0x20000120   Data           4  my_adc.o(.data)
    AC_current_out                           0x20000124   Data           4  my_adc.o(.data)
    voltage_out                              0x20000128   Data           4  my_adc.o(.data)
    current_out                              0x2000012c   Data           4  my_adc.o(.data)
    dc_voltage_raw                           0x20000130   Data           4  my_adc.o(.data)
    dc_current_raw                           0x20000134   Data           4  my_adc.o(.data)
    DC_voltage_out                           0x20000138   Data           4  my_adc.o(.data)
    DC_current_out                           0x2000013c   Data           4  my_adc.o(.data)
    dc_voltage_out                           0x20000140   Data           4  my_adc.o(.data)
    dc_current_out                           0x20000144   Data           4  my_adc.o(.data)
    fontaddr                                 0x2000014c   Data           4  oled.o(.data)
    depth                                    0x20000150   Data           4  task.o(.data)
    balance                                  0x20000154   Data           4  task.o(.data)
    spwm_cnt                                 0x20000158   Data           4  task.o(.data)
    Frequency                                0x2000015c   Data           4  task.o(.data)
    oled_flag                                0x20000160   Data           1  task.o(.data)
    bing_wang_flag                           0x20000161   Data           1  task.o(.data)
    key_num                                  0x20000162   Data           2  task.o(.data)
    set_current                              0x20000164   Data           4  task.o(.data)
    voltage_control_flag                     0x20000174   Data           1  pid_control.o(.data)
    current_control_flag                     0x20000175   Data           1  pid_control.o(.data)
    target_current                           0x20000178   Data           4  pid_control.o(.data)
    dc_pid_mode                              0x2000017c   Data           1  pid_control.o(.data)
    dc_target_voltage                        0x20000180   Data           4  pid_control.o(.data)
    dc_target_current                        0x20000184   Data           4  pid_control.o(.data)
    dc_pwm_output                            0x20000188   Data           2  pid_control.o(.data)
    dc_pid_enable                            0x2000018a   Data           1  pid_control.o(.data)
    __stdout                                 0x20000190   Data           4  stdout.o(.data)
    hadc1                                    0x20000194   Data         108  adc.o(.bss)
    hdma_adc1                                0x20000200   Data          96  adc.o(.bss)
    htim1                                    0x20000260   Data          76  tim.o(.bss)
    htim2                                    0x200002ac   Data          76  tim.o(.bss)
    htim3                                    0x200002f8   Data          76  tim.o(.bss)
    htim4                                    0x20000344   Data          76  tim.o(.bss)
    htim6                                    0x20000390   Data          76  tim.o(.bss)
    htim7                                    0x200003dc   Data          76  tim.o(.bss)
    htim15                                   0x20000428   Data          76  tim.o(.bss)
    rx_buffer                                0x20000474   Data         100  usart.o(.bss)
    huart1                                   0x200004d8   Data         148  usart.o(.bss)
    filter_buffer_voltage                    0x2000056c   Data          40  my_adc.o(.bss)
    filter_buffer_current                    0x20000594   Data          40  my_adc.o(.bss)
    filter_buffer_dc_voltage                 0x200005bc   Data          40  my_adc.o(.bss)
    filter_buffer_dc_current                 0x200005e4   Data          40  my_adc.o(.bss)
    AC_Voltage_PID                           0x200006d4   Data          40  pid_yyds.o(.bss)
    AC_Current_PID                           0x200006fc   Data          40  pid_yyds.o(.bss)
    AC_Frequency_PID                         0x20000724   Data          40  pid_yyds.o(.bss)
    SPWM_Depth_PID                           0x2000074c   Data          40  pid_yyds.o(.bss)
    Current_Control_PID                      0x20000774   Data          40  pid_yyds.o(.bss)
    pid_control_state                        0x2000079c   Data          24  pid_control.o(.bss)
    dc_voltage_pid                           0x200007b4   Data          48  pid_control.o(.bss)
    dc_current_pid                           0x200007e4   Data          48  pid_control.o(.bss)
    __initial_sp                             0x20000c18   Data           0  startup_stm32g431xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001d9

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00009e88, Max: 0x00020000, ABSOLUTE, COMPRESSED[0x00009d54])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00009cf4, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001d8   Data   RO            3    RESET               startup_stm32g431xx.o
    0x080001d8   0x080001d8   0x00000000   Code   RO         5185  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001d8   0x080001d8   0x00000004   Code   RO         5457    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001dc   0x080001dc   0x00000004   Code   RO         5460    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001e0   0x080001e0   0x00000000   Code   RO         5462    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001e0   0x080001e0   0x00000000   Code   RO         5464    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001e0   0x080001e0   0x00000008   Code   RO         5465    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001e8   0x080001e8   0x00000004   Code   RO         5472    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x080001ec   0x080001ec   0x00000000   Code   RO         5467    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x080001ec   0x080001ec   0x00000000   Code   RO         5469    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x080001ec   0x080001ec   0x00000004   Code   RO         5458    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001f0   0x080001f0   0x00000024   Code   RO            4    .text               startup_stm32g431xx.o
    0x08000214   0x08000214   0x00000062   Code   RO         5188    .text               mc_w.l(uldiv.o)
    0x08000276   0x08000276   0x00000024   Code   RO         5190    .text               mc_w.l(memseta.o)
    0x0800029a   0x0800029a   0x0000001c   Code   RO         5192    .text               mc_w.l(strcmp.o)
    0x080002b6   0x080002b6   0x00000026   Code   RO         5455    .text               mf_w.l(f2d.o)
    0x080002dc   0x080002dc   0x0000002c   Code   RO         5474    .text               mc_w.l(uidiv.o)
    0x08000308   0x08000308   0x0000001e   Code   RO         5476    .text               mc_w.l(llshl.o)
    0x08000326   0x08000326   0x00000020   Code   RO         5478    .text               mc_w.l(llushr.o)
    0x08000346   0x08000346   0x00000000   Code   RO         5480    .text               mc_w.l(iusefp.o)
    0x08000346   0x08000346   0x0000014e   Code   RO         5481    .text               mf_w.l(dadd.o)
    0x08000494   0x08000494   0x000000e4   Code   RO         5483    .text               mf_w.l(dmul.o)
    0x08000578   0x08000578   0x000000de   Code   RO         5485    .text               mf_w.l(ddiv.o)
    0x08000656   0x08000656   0x00000030   Code   RO         5487    .text               mf_w.l(dfixul.o)
    0x08000686   0x08000686   0x00000002   PAD
    0x08000688   0x08000688   0x00000030   Code   RO         5489    .text               mf_w.l(cdrcmple.o)
    0x080006b8   0x080006b8   0x00000024   Code   RO         5491    .text               mc_w.l(init.o)
    0x080006dc   0x080006dc   0x00000024   Code   RO         5493    .text               mc_w.l(llsshr.o)
    0x08000700   0x08000700   0x000000ba   Code   RO         5495    .text               mf_w.l(depilogue.o)
    0x080007ba   0x080007ba   0x00000056   Code   RO         5505    .text               mc_w.l(__dczerorl2.o)
    0x08000810   0x08000810   0x0000011c   Code   RO          603    i.ADC_ConversionStop  stm32g4xx_hal_adc.o
    0x0800092c   0x0800092c   0x00000092   Code   RO          604    i.ADC_DMAConvCplt   stm32g4xx_hal_adc.o
    0x080009be   0x080009be   0x0000001e   Code   RO          605    i.ADC_DMAError      stm32g4xx_hal_adc.o
    0x080009dc   0x080009dc   0x0000000e   Code   RO          606    i.ADC_DMAHalfConvCplt  stm32g4xx_hal_adc.o
    0x080009ea   0x080009ea   0x00000002   PAD
    0x080009ec   0x080009ec   0x00000094   Code   RO          607    i.ADC_Disable       stm32g4xx_hal_adc.o
    0x08000a80   0x08000a80   0x000000c8   Code   RO          608    i.ADC_Enable        stm32g4xx_hal_adc.o
    0x08000b48   0x08000b48   0x00000004   Code   RO          467    i.BusFault_Handler  stm32g4xx_it.o
    0x08000b4c   0x08000b4c   0x000000f0   Code   RO         4548    i.Calculate_ADC     my_adc.o
    0x08000c3c   0x08000c3c   0x000000e4   Code   RO         4550    i.Calculate_DC_ADC  my_adc.o
    0x08000d20   0x08000d20   0x0000000e   Code   RO         4487    i.Compare_Min       menu.o
    0x08000d2e   0x08000d2e   0x00000002   PAD
    0x08000d30   0x08000d30   0x0000003c   Code   RO         5006    i.DC_Disable_PID_Control  pid_control.o
    0x08000d6c   0x08000d6c   0x00000078   Code   RO         5007    i.DC_Enable_Constant_Current  pid_control.o
    0x08000de4   0x08000de4   0x00000078   Code   RO         5008    i.DC_Enable_Constant_Voltage  pid_control.o
    0x08000e5c   0x08000e5c   0x0000007c   Code   RO         5009    i.DC_Get_PID_Status  pid_control.o
    0x08000ed8   0x08000ed8   0x00000134   Code   RO         5010    i.DC_PID_Calculate  pid_control.o
    0x0800100c   0x0800100c   0x000000ac   Code   RO         5012    i.DC_PID_Control_Loop  pid_control.o
    0x080010b8   0x080010b8   0x00000038   Code   RO         5014    i.DC_PID_Reset      pid_control.o
    0x080010f0   0x080010f0   0x00000020   Code   RO         5018    i.DC_Set_PWM_Output  pid_control.o
    0x08001110   0x08001110   0x00000010   Code   RO          468    i.DMA1_Channel1_IRQHandler  stm32g4xx_it.o
    0x08001120   0x08001120   0x00000044   Code   RO         2026    i.DMA_CalcDMAMUXChannelBaseAndMask  stm32g4xx_hal_dma.o
    0x08001164   0x08001164   0x00000024   Code   RO         2027    i.DMA_CalcDMAMUXRequestGenBaseAndMask  stm32g4xx_hal_dma.o
    0x08001188   0x08001188   0x00000040   Code   RO         2028    i.DMA_SetConfig     stm32g4xx_hal_dma.o
    0x080011c8   0x080011c8   0x00000002   Code   RO          469    i.DebugMon_Handler  stm32g4xx_it.o
    0x080011ca   0x080011ca   0x00000002   PAD
    0x080011cc   0x080011cc   0x0000006c   Code   RO         4426    i.Encode_scan       key.o
    0x08001238   0x08001238   0x00000006   Code   RO           13    i.Error_Handler     main.o
    0x0800123e   0x0800123e   0x00000002   PAD
    0x08001240   0x08001240   0x00000048   Code   RO         4664    i.Get_data_from_ROM  oled.o
    0x08001288   0x08001288   0x0000010c   Code   RO          898    i.HAL_ADCEx_MultiModeConfigChannel  stm32g4xx_hal_adc_ex.o
    0x08001394   0x08001394   0x00000508   Code   RO          610    i.HAL_ADC_ConfigChannel  stm32g4xx_hal_adc.o
    0x0800189c   0x0800189c   0x00000002   Code   RO          611    i.HAL_ADC_ConvCpltCallback  stm32g4xx_hal_adc.o
    0x0800189e   0x0800189e   0x00000002   Code   RO          612    i.HAL_ADC_ConvHalfCpltCallback  stm32g4xx_hal_adc.o
    0x080018a0   0x080018a0   0x00000002   Code   RO          614    i.HAL_ADC_ErrorCallback  stm32g4xx_hal_adc.o
    0x080018a2   0x080018a2   0x00000006   Code   RO          616    i.HAL_ADC_GetState  stm32g4xx_hal_adc.o
    0x080018a8   0x080018a8   0x0000025c   Code   RO          619    i.HAL_ADC_Init      stm32g4xx_hal_adc.o
    0x08001b04   0x08001b04   0x000000cc   Code   RO          247    i.HAL_ADC_MspInit   adc.o
    0x08001bd0   0x08001bd0   0x00000114   Code   RO          627    i.HAL_ADC_Start_DMA  stm32g4xx_hal_adc.o
    0x08001ce4   0x08001ce4   0x00000088   Code   RO          631    i.HAL_ADC_Stop_DMA  stm32g4xx_hal_adc.o
    0x08001d6c   0x08001d6c   0x00000076   Code   RO         2029    i.HAL_DMA_Abort     stm32g4xx_hal_dma.o
    0x08001de2   0x08001de2   0x00000094   Code   RO         2030    i.HAL_DMA_Abort_IT  stm32g4xx_hal_dma.o
    0x08001e76   0x08001e76   0x000000fe   Code   RO         2034    i.HAL_DMA_IRQHandler  stm32g4xx_hal_dma.o
    0x08001f74   0x08001f74   0x000000d0   Code   RO         2035    i.HAL_DMA_Init      stm32g4xx_hal_dma.o
    0x08002044   0x08002044   0x000000c2   Code   RO         2039    i.HAL_DMA_Start_IT  stm32g4xx_hal_dma.o
    0x08002106   0x08002106   0x00000002   PAD
    0x08002108   0x08002108   0x00000028   Code   RO         1122    i.HAL_Delay         stm32g4xx_hal.o
    0x08002130   0x08002130   0x000001cc   Code   RO         1891    i.HAL_GPIO_Init     stm32g4xx_hal_gpio.o
    0x080022fc   0x080022fc   0x00000010   Code   RO         1893    i.HAL_GPIO_ReadPin  stm32g4xx_hal_gpio.o
    0x0800230c   0x0800230c   0x0000000a   Code   RO         1895    i.HAL_GPIO_WritePin  stm32g4xx_hal_gpio.o
    0x08002316   0x08002316   0x00000002   PAD
    0x08002318   0x08002318   0x0000000c   Code   RO         1126    i.HAL_GetTick       stm32g4xx_hal.o
    0x08002324   0x08002324   0x00000018   Code   RO         1132    i.HAL_IncTick       stm32g4xx_hal.o
    0x0800233c   0x0800233c   0x0000001e   Code   RO         1133    i.HAL_Init          stm32g4xx_hal.o
    0x0800235a   0x0800235a   0x00000002   PAD
    0x0800235c   0x0800235c   0x00000058   Code   RO         1134    i.HAL_InitTick      stm32g4xx_hal.o
    0x080023b4   0x080023b4   0x00000044   Code   RO          579    i.HAL_MspInit       stm32g4xx_hal_msp.o
    0x080023f8   0x080023f8   0x00000028   Code   RO         2540    i.HAL_NVIC_EnableIRQ  stm32g4xx_hal_cortex.o
    0x08002420   0x08002420   0x0000007a   Code   RO         2546    i.HAL_NVIC_SetPriority  stm32g4xx_hal_cortex.o
    0x0800249a   0x0800249a   0x00000002   PAD
    0x0800249c   0x0800249c   0x00000028   Code   RO         2547    i.HAL_NVIC_SetPriorityGrouping  stm32g4xx_hal_cortex.o
    0x080024c4   0x080024c4   0x00000118   Code   RO         2294    i.HAL_PWREx_ControlVoltageScaling  stm32g4xx_hal_pwr_ex.o
    0x080025dc   0x080025dc   0x00000014   Code   RO         2306    i.HAL_PWREx_DisableUCPDDeadBattery  stm32g4xx_hal_pwr_ex.o
    0x080025f0   0x080025f0   0x00000370   Code   RO         1498    i.HAL_RCCEx_PeriphCLKConfig  stm32g4xx_hal_rcc_ex.o
    0x08002960   0x08002960   0x00000224   Code   RO         1368    i.HAL_RCC_ClockConfig  stm32g4xx_hal_rcc.o
    0x08002b84   0x08002b84   0x0000000c   Code   RO         1374    i.HAL_RCC_GetHCLKFreq  stm32g4xx_hal_rcc.o
    0x08002b90   0x08002b90   0x00000024   Code   RO         1376    i.HAL_RCC_GetPCLK1Freq  stm32g4xx_hal_rcc.o
    0x08002bb4   0x08002bb4   0x00000024   Code   RO         1377    i.HAL_RCC_GetPCLK2Freq  stm32g4xx_hal_rcc.o
    0x08002bd8   0x08002bd8   0x00000098   Code   RO         1378    i.HAL_RCC_GetSysClockFreq  stm32g4xx_hal_rcc.o
    0x08002c70   0x08002c70   0x0000055c   Code   RO         1381    i.HAL_RCC_OscConfig  stm32g4xx_hal_rcc.o
    0x080031cc   0x080031cc   0x00000034   Code   RO         2551    i.HAL_SYSTICK_Config  stm32g4xx_hal_cortex.o
    0x08003200   0x08003200   0x00000002   Code   RO         3412    i.HAL_TIMEx_Break2Callback  stm32g4xx_hal_tim_ex.o
    0x08003202   0x08003202   0x00000002   Code   RO         3413    i.HAL_TIMEx_BreakCallback  stm32g4xx_hal_tim_ex.o
    0x08003204   0x08003204   0x00000002   Code   RO         3414    i.HAL_TIMEx_CommutCallback  stm32g4xx_hal_tim_ex.o
    0x08003206   0x08003206   0x00000002   PAD
    0x08003208   0x08003208   0x000000c8   Code   RO         3417    i.HAL_TIMEx_ConfigBreakDeadTime  stm32g4xx_hal_tim_ex.o
    0x080032d0   0x080032d0   0x00000002   Code   RO         3425    i.HAL_TIMEx_DirectionChangeCallback  stm32g4xx_hal_tim_ex.o
    0x080032d2   0x080032d2   0x00000012   Code   RO         3435    i.HAL_TIMEx_EnableDeadTimePreload  stm32g4xx_hal_tim_ex.o
    0x080032e4   0x080032e4   0x00000002   Code   RO         3439    i.HAL_TIMEx_EncoderIndexCallback  stm32g4xx_hal_tim_ex.o
    0x080032e6   0x080032e6   0x00000002   Code   RO         3453    i.HAL_TIMEx_IndexErrorCallback  stm32g4xx_hal_tim_ex.o
    0x080032e8   0x080032e8   0x000000b8   Code   RO         3454    i.HAL_TIMEx_MasterConfigSynchronization  stm32g4xx_hal_tim_ex.o
    0x080033a0   0x080033a0   0x00000148   Code   RO         3468    i.HAL_TIMEx_PWMN_Start_IT  stm32g4xx_hal_tim_ex.o
    0x080034e8   0x080034e8   0x00000002   Code   RO         3475    i.HAL_TIMEx_TransitionErrorCallback  stm32g4xx_hal_tim_ex.o
    0x080034ea   0x080034ea   0x0000006e   Code   RO         2687    i.HAL_TIM_Base_Init  stm32g4xx_hal_tim.o
    0x08003558   0x08003558   0x000001cc   Code   RO          313    i.HAL_TIM_Base_MspInit  tim.o
    0x08003724   0x08003724   0x00000090   Code   RO         2690    i.HAL_TIM_Base_Start  stm32g4xx_hal_tim.o
    0x080037b4   0x080037b4   0x0000009c   Code   RO         2692    i.HAL_TIM_Base_Start_IT  stm32g4xx_hal_tim.o
    0x08003850   0x08003850   0x00000134   Code   RO         2696    i.HAL_TIM_ConfigClockSource  stm32g4xx_hal_tim.o
    0x08003984   0x08003984   0x000000cc   Code   RO         2708    i.HAL_TIM_Encoder_Init  stm32g4xx_hal_tim.o
    0x08003a50   0x08003a50   0x00000078   Code   RO          315    i.HAL_TIM_Encoder_MspInit  tim.o
    0x08003ac8   0x08003ac8   0x000000cc   Code   RO         2711    i.HAL_TIM_Encoder_Start  stm32g4xx_hal_tim.o
    0x08003b94   0x08003b94   0x000000a8   Code   RO         4810    i.HAL_TIM_IC_CaptureCallback  task.o
    0x08003c3c   0x08003c3c   0x000000de   Code   RO         2723    i.HAL_TIM_IC_ConfigChannel  stm32g4xx_hal_tim.o
    0x08003d1a   0x08003d1a   0x0000006e   Code   RO         2726    i.HAL_TIM_IC_Init   stm32g4xx_hal_tim.o
    0x08003d88   0x08003d88   0x00000002   Code   RO         2728    i.HAL_TIM_IC_MspInit  stm32g4xx_hal_tim.o
    0x08003d8a   0x08003d8a   0x00000002   PAD
    0x08003d8c   0x08003d8c   0x000001b0   Code   RO         2731    i.HAL_TIM_IC_Start_IT  stm32g4xx_hal_tim.o
    0x08003f3c   0x08003f3c   0x00000220   Code   RO         2735    i.HAL_TIM_IRQHandler  stm32g4xx_hal_tim.o
    0x0800415c   0x0800415c   0x000000cc   Code   RO          316    i.HAL_TIM_MspPostInit  tim.o
    0x08004228   0x08004228   0x00000002   Code   RO         2738    i.HAL_TIM_OC_DelayElapsedCallback  stm32g4xx_hal_tim.o
    0x0800422a   0x0800422a   0x00000170   Code   RO         2759    i.HAL_TIM_PWM_ConfigChannel  stm32g4xx_hal_tim.o
    0x0800439a   0x0800439a   0x0000006e   Code   RO         2762    i.HAL_TIM_PWM_Init  stm32g4xx_hal_tim.o
    0x08004408   0x08004408   0x00000002   Code   RO         2764    i.HAL_TIM_PWM_MspInit  stm32g4xx_hal_tim.o
    0x0800440a   0x0800440a   0x00000002   Code   RO         2765    i.HAL_TIM_PWM_PulseFinishedCallback  stm32g4xx_hal_tim.o
    0x0800440c   0x0800440c   0x0000014c   Code   RO         2767    i.HAL_TIM_PWM_Start  stm32g4xx_hal_tim.o
    0x08004558   0x08004558   0x000001a0   Code   RO         2769    i.HAL_TIM_PWM_Start_IT  stm32g4xx_hal_tim.o
    0x080046f8   0x080046f8   0x000000a8   Code   RO         4811    i.HAL_TIM_PeriodElapsedCallback  task.o
    0x080047a0   0x080047a0   0x00000032   Code   RO         2775    i.HAL_TIM_ReadCapturedValue  stm32g4xx_hal_tim.o
    0x080047d2   0x080047d2   0x00000002   Code   RO         2778    i.HAL_TIM_TriggerCallback  stm32g4xx_hal_tim.o
    0x080047d4   0x080047d4   0x0000004e   Code   RO         4266    i.HAL_UARTEx_DisableFifoMode  stm32g4xx_hal_uart_ex.o
    0x08004822   0x08004822   0x00000002   Code   RO         3850    i.HAL_UARTEx_RxEventCallback  stm32g4xx_hal_uart.o
    0x08004824   0x08004824   0x00000002   Code   RO         4274    i.HAL_UARTEx_RxFifoFullCallback  stm32g4xx_hal_uart_ex.o
    0x08004826   0x08004826   0x0000005e   Code   RO         4275    i.HAL_UARTEx_SetRxFifoThreshold  stm32g4xx_hal_uart_ex.o
    0x08004884   0x08004884   0x0000005e   Code   RO         4276    i.HAL_UARTEx_SetTxFifoThreshold  stm32g4xx_hal_uart_ex.o
    0x080048e2   0x080048e2   0x00000002   Code   RO         4278    i.HAL_UARTEx_TxFifoEmptyCallback  stm32g4xx_hal_uart_ex.o
    0x080048e4   0x080048e4   0x00000002   Code   RO         4279    i.HAL_UARTEx_WakeupCallback  stm32g4xx_hal_uart_ex.o
    0x080048e6   0x080048e6   0x00000002   Code   RO         3866    i.HAL_UART_ErrorCallback  stm32g4xx_hal_uart.o
    0x080048e8   0x080048e8   0x000003d0   Code   RO         3869    i.HAL_UART_IRQHandler  stm32g4xx_hal_uart.o
    0x08004cb8   0x08004cb8   0x00000078   Code   RO         3870    i.HAL_UART_Init     stm32g4xx_hal_uart.o
    0x08004d30   0x08004d30   0x000000a4   Code   RO          409    i.HAL_UART_MspInit  usart.o
    0x08004dd4   0x08004dd4   0x0000005c   Code   RO         3875    i.HAL_UART_Receive_IT  stm32g4xx_hal_uart.o
    0x08004e30   0x08004e30   0x00000002   Code   RO         3877    i.HAL_UART_RxCpltCallback  stm32g4xx_hal_uart.o
    0x08004e32   0x08004e32   0x000000c2   Code   RO         3879    i.HAL_UART_Transmit  stm32g4xx_hal_uart.o
    0x08004ef4   0x08004ef4   0x00000002   Code   RO         3882    i.HAL_UART_TxCpltCallback  stm32g4xx_hal_uart.o
    0x08004ef6   0x08004ef6   0x00000004   Code   RO          470    i.HardFault_Handler  stm32g4xx_it.o
    0x08004efa   0x08004efa   0x00000002   PAD
    0x08004efc   0x08004efc   0x00000010   Code   RO          633    i.LL_ADC_Enable     stm32g4xx_hal_adc.o
    0x08004f0c   0x08004f0c   0x0000000a   Code   RO          634    i.LL_ADC_GetMultimode  stm32g4xx_hal_adc.o
    0x08004f16   0x08004f16   0x00000012   Code   RO          635    i.LL_ADC_GetOffsetChannel  stm32g4xx_hal_adc.o
    0x08004f28   0x08004f28   0x0000000a   Code   RO          636    i.LL_ADC_INJ_IsConversionOngoing  stm32g4xx_hal_adc.o
    0x08004f32   0x08004f32   0x0000000a   Code   RO          637    i.LL_ADC_IsDisableOngoing  stm32g4xx_hal_adc.o
    0x08004f3c   0x08004f3c   0x0000000a   Code   RO          638    i.LL_ADC_IsEnabled  stm32g4xx_hal_adc.o
    0x08004f46   0x08004f46   0x0000000a   Code   RO          910    i.LL_ADC_IsEnabled  stm32g4xx_hal_adc_ex.o
    0x08004f50   0x08004f50   0x0000000a   Code   RO          639    i.LL_ADC_IsInternalRegulatorEnabled  stm32g4xx_hal_adc.o
    0x08004f5a   0x08004f5a   0x0000000a   Code   RO          640    i.LL_ADC_REG_IsConversionOngoing  stm32g4xx_hal_adc.o
    0x08004f64   0x08004f64   0x0000000a   Code   RO          911    i.LL_ADC_REG_IsConversionOngoing  stm32g4xx_hal_adc_ex.o
    0x08004f6e   0x08004f6e   0x00000012   Code   RO          641    i.LL_ADC_REG_IsTriggerSourceSWStart  stm32g4xx_hal_adc.o
    0x08004f80   0x08004f80   0x00000010   Code   RO          642    i.LL_ADC_REG_StartConversion  stm32g4xx_hal_adc.o
    0x08004f90   0x08004f90   0x00000028   Code   RO          644    i.LL_ADC_SetChannelSamplingTime  stm32g4xx_hal_adc.o
    0x08004fb8   0x08004fb8   0x0000000c   Code   RO          645    i.LL_ADC_SetCommonPathInternalCh  stm32g4xx_hal_adc.o
    0x08004fc4   0x08004fc4   0x00000016   Code   RO          646    i.LL_ADC_SetOffsetState  stm32g4xx_hal_adc.o
    0x08004fda   0x08004fda   0x0000000c   Code   RO          647    i.LL_ADC_SetSamplingTimeCommonConfig  stm32g4xx_hal_adc.o
    0x08004fe6   0x08004fe6   0x00000002   PAD
    0x08004fe8   0x08004fe8   0x000000f4   Code   RO          248    i.MX_ADC1_Init      adc.o
    0x080050dc   0x080050dc   0x00000050   Code   RO          288    i.MX_DMA_Init       dma.o
    0x0800512c   0x0800512c   0x0000013c   Code   RO          222    i.MX_GPIO_Init      gpio.o
    0x08005268   0x08005268   0x000000a4   Code   RO          317    i.MX_TIM15_Init     tim.o
    0x0800530c   0x0800530c   0x0000011c   Code   RO          318    i.MX_TIM1_Init      tim.o
    0x08005428   0x08005428   0x000000a0   Code   RO          319    i.MX_TIM2_Init      tim.o
    0x080054c8   0x080054c8   0x00000070   Code   RO          320    i.MX_TIM3_Init      tim.o
    0x08005538   0x08005538   0x0000007c   Code   RO          321    i.MX_TIM4_Init      tim.o
    0x080055b4   0x080055b4   0x0000004c   Code   RO          322    i.MX_TIM6_Init      tim.o
    0x08005600   0x08005600   0x0000004c   Code   RO          323    i.MX_TIM7_Init      tim.o
    0x0800564c   0x0800564c   0x0000006c   Code   RO          410    i.MX_USART1_UART_Init  usart.o
    0x080056b8   0x080056b8   0x00000004   Code   RO          471    i.MemManage_Handler  stm32g4xx_it.o
    0x080056bc   0x080056bc   0x00000254   Code   RO         4488    i.Menu              menu.o
    0x08005910   0x08005910   0x00000064   Code   RO         4489    i.Menu_Init         menu.o
    0x08005974   0x08005974   0x00000004   Code   RO          472    i.NMI_Handler       stm32g4xx_it.o
    0x08005978   0x08005978   0x00000040   Code   RO         4666    i.OLED_Clear        oled.o
    0x080059b8   0x080059b8   0x0000001e   Code   RO         4667    i.OLED_ColorTurn    oled.o
    0x080059d6   0x080059d6   0x0000002e   Code   RO         4670    i.OLED_DisplayTurn  oled.o
    0x08005a04   0x08005a04   0x00000052   Code   RO         4672    i.OLED_Display_16x16  oled.o
    0x08005a56   0x08005a56   0x00000052   Code   RO         4674    i.OLED_Display_8x16  oled.o
    0x08005aa8   0x08005aa8   0x00000180   Code   RO         4675    i.OLED_Display_GB2312_string  oled.o
    0x08005c28   0x08005c28   0x000000de   Code   RO         4677    i.OLED_Init         oled.o
    0x08005d06   0x08005d06   0x00000002   PAD
    0x08005d08   0x08005d08   0x00000140   Code   RO         4678    i.OLED_ShowNum      oled.o
    0x08005e48   0x08005e48   0x0000008c   Code   RO         4679    i.OLED_WR_Byte      oled.o
    0x08005ed4   0x08005ed4   0x0000002e   Code   RO         4680    i.OLED_address      oled.o
    0x08005f02   0x08005f02   0x00000002   PAD
    0x08005f04   0x08005f04   0x00000058   Code   RO         4681    i.OLED_get_data_from_ROM  oled.o
    0x08005f5c   0x08005f5c   0x00000224   Code   RO         5020    i.PID_Control_Current_Process  pid_control.o
    0x08006180   0x08006180   0x0000000a   Code   RO         5021    i.PID_Control_Disable_All  pid_control.o
    0x0800618a   0x0800618a   0x00000002   PAD
    0x0800618c   0x0800618c   0x00000074   Code   RO         5022    i.PID_Control_Enable_Current  pid_control.o
    0x08006200   0x08006200   0x00000068   Code   RO         5023    i.PID_Control_Enable_Voltage  pid_control.o
    0x08006268   0x08006268   0x00000080   Code   RO         5026    i.PID_Control_Init  pid_control.o
    0x080062e8   0x080062e8   0x0000002c   Code   RO         5027    i.PID_Control_Loop  pid_control.o
    0x08006314   0x08006314   0x00000100   Code   RO         5029    i.PID_Control_Set_Mode  pid_control.o
    0x08006414   0x08006414   0x00000274   Code   RO         5032    i.PID_Control_Voltage_Process  pid_control.o
    0x08006688   0x08006688   0x0000011c   Code   RO         4911    i.PID_Current_Control  pid_yyds.o
    0x080067a4   0x080067a4   0x00000234   Code   RO         4912    i.PID_Debug_Info    pid_yyds.o
    0x080069d8   0x080069d8   0x00000044   Code   RO         4915    i.PID_Init          pid_yyds.o
    0x08006a1c   0x08006a1c   0x00000034   Code   RO         4917    i.PID_Reset         pid_yyds.o
    0x08006a50   0x08006a50   0x0000011c   Code   RO         4919    i.PID_SPWM_Depth_Control  pid_yyds.o
    0x08006b6c   0x08006b6c   0x00000002   Code   RO          473    i.PendSV_Handler    stm32g4xx_it.o
    0x08006b6e   0x08006b6e   0x00000002   PAD
    0x08006b70   0x08006b70   0x00000068   Code   RO         1382    i.RCC_GetSysClockFreqFromPLLSource  stm32g4xx_hal_rcc.o
    0x08006bd8   0x08006bd8   0x00000002   Code   RO          474    i.SVC_Handler       stm32g4xx_it.o
    0x08006bda   0x08006bda   0x00000002   PAD
    0x08006bdc   0x08006bdc   0x00000050   Code   RO         4682    i.Send_Command_to_ROM  oled.o
    0x08006c2c   0x08006c2c   0x00000008   Code   RO          475    i.SysTick_Handler   stm32g4xx_it.o
    0x08006c34   0x08006c34   0x00000068   Code   RO           14    i.SystemClock_Config  main.o
    0x08006c9c   0x08006c9c   0x00000014   Code   RO         4390    i.SystemInit        system_stm32g4xx.o
    0x08006cb0   0x08006cb0   0x00000018   Code   RO          476    i.TIM1_BRK_TIM15_IRQHandler  stm32g4xx_it.o
    0x08006cc8   0x08006cc8   0x00000010   Code   RO          477    i.TIM2_IRQHandler   stm32g4xx_it.o
    0x08006cd8   0x08006cd8   0x00000010   Code   RO          478    i.TIM6_DAC_IRQHandler  stm32g4xx_it.o
    0x08006ce8   0x08006ce8   0x00000010   Code   RO          479    i.TIM7_IRQHandler   stm32g4xx_it.o
    0x08006cf8   0x08006cf8   0x000000cc   Code   RO         2780    i.TIM_Base_SetConfig  stm32g4xx_hal_tim.o
    0x08006dc4   0x08006dc4   0x00000022   Code   RO         2781    i.TIM_CCxChannelCmd  stm32g4xx_hal_tim.o
    0x08006de6   0x08006de6   0x00000022   Code   RO         3478    i.TIM_CCxNChannelCmd  stm32g4xx_hal_tim_ex.o
    0x08006e08   0x08006e08   0x00000016   Code   RO         2791    i.TIM_ETR_SetConfig  stm32g4xx_hal_tim.o
    0x08006e1e   0x08006e1e   0x00000002   PAD
    0x08006e20   0x08006e20   0x00000018   Code   RO         2792    i.TIM_ITRx_SetConfig  stm32g4xx_hal_tim.o
    0x08006e38   0x08006e38   0x000000ac   Code   RO         2793    i.TIM_OC1_SetConfig  stm32g4xx_hal_tim.o
    0x08006ee4   0x08006ee4   0x000000b4   Code   RO         2794    i.TIM_OC2_SetConfig  stm32g4xx_hal_tim.o
    0x08006f98   0x08006f98   0x000000b4   Code   RO         2795    i.TIM_OC3_SetConfig  stm32g4xx_hal_tim.o
    0x0800704c   0x0800704c   0x000000b4   Code   RO         2796    i.TIM_OC4_SetConfig  stm32g4xx_hal_tim.o
    0x08007100   0x08007100   0x00000070   Code   RO         2797    i.TIM_OC5_SetConfig  stm32g4xx_hal_tim.o
    0x08007170   0x08007170   0x00000070   Code   RO         2798    i.TIM_OC6_SetConfig  stm32g4xx_hal_tim.o
    0x080071e0   0x080071e0   0x00000026   Code   RO         2800    i.TIM_TI1_ConfigInputStage  stm32g4xx_hal_tim.o
    0x08007206   0x08007206   0x00000002   PAD
    0x08007208   0x08007208   0x0000007c   Code   RO         2801    i.TIM_TI1_SetConfig  stm32g4xx_hal_tim.o
    0x08007284   0x08007284   0x00000028   Code   RO         2802    i.TIM_TI2_ConfigInputStage  stm32g4xx_hal_tim.o
    0x080072ac   0x080072ac   0x0000003a   Code   RO         2803    i.TIM_TI2_SetConfig  stm32g4xx_hal_tim.o
    0x080072e6   0x080072e6   0x00000038   Code   RO         2804    i.TIM_TI3_SetConfig  stm32g4xx_hal_tim.o
    0x0800731e   0x0800731e   0x0000003c   Code   RO         2805    i.TIM_TI4_SetConfig  stm32g4xx_hal_tim.o
    0x0800735a   0x0800735a   0x00000002   PAD
    0x0800735c   0x0800735c   0x00000058   Code   RO         4280    i.UARTEx_SetNbDataToProcess  stm32g4xx_hal_uart_ex.o
    0x080073b4   0x080073b4   0x000000f8   Code   RO         3884    i.UART_AdvFeatureConfig  stm32g4xx_hal_uart.o
    0x080074ac   0x080074ac   0x000000ea   Code   RO         3885    i.UART_CheckIdleState  stm32g4xx_hal_uart.o
    0x08007596   0x08007596   0x00000014   Code   RO         3886    i.UART_DMAAbortOnError  stm32g4xx_hal_uart.o
    0x080075aa   0x080075aa   0x00000002   PAD
    0x080075ac   0x080075ac   0x0000006c   Code   RO         3896    i.UART_EndRxTransfer  stm32g4xx_hal_uart.o
    0x08007618   0x08007618   0x00000030   Code   RO         3897    i.UART_EndTransmit_IT  stm32g4xx_hal_uart.o
    0x08007648   0x08007648   0x00000104   Code   RO         3899    i.UART_RxISR_16BIT  stm32g4xx_hal_uart.o
    0x0800774c   0x0800774c   0x00000200   Code   RO         3900    i.UART_RxISR_16BIT_FIFOEN  stm32g4xx_hal_uart.o
    0x0800794c   0x0800794c   0x00000104   Code   RO         3901    i.UART_RxISR_8BIT   stm32g4xx_hal_uart.o
    0x08007a50   0x08007a50   0x000001fc   Code   RO         3902    i.UART_RxISR_8BIT_FIFOEN  stm32g4xx_hal_uart.o
    0x08007c4c   0x08007c4c   0x00000384   Code   RO         3903    i.UART_SetConfig    stm32g4xx_hal_uart.o
    0x08007fd0   0x08007fd0   0x0000015c   Code   RO         3905    i.UART_Start_Receive_IT  stm32g4xx_hal_uart.o
    0x0800812c   0x0800812c   0x000000ac   Code   RO         3910    i.UART_WaitOnFlagUntilTimeout  stm32g4xx_hal_uart.o
    0x080081d8   0x080081d8   0x00000010   Code   RO          480    i.USART1_IRQHandler  stm32g4xx_it.o
    0x080081e8   0x080081e8   0x00000004   Code   RO          481    i.UsageFault_Handler  stm32g4xx_it.o
    0x080081ec   0x080081ec   0x00000020   Code   RO         5427    i.__0printf         mc_w.l(printfa.o)
    0x0800820c   0x0800820c   0x00000010   Code   RO         2553    i.__NVIC_GetPriorityGrouping  stm32g4xx_hal_cortex.o
    0x0800821c   0x0800821c   0x00000028   Code   RO         2554    i.__NVIC_SetPriority  stm32g4xx_hal_cortex.o
    0x08008244   0x08008244   0x0000000e   Code   RO         5499    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08008252   0x08008252   0x00000002   Code   RO         5500    i.__scatterload_null  mc_w.l(handlers.o)
    0x08008254   0x08008254   0x0000000e   Code   RO         5501    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08008262   0x08008262   0x00000002   PAD
    0x08008264   0x08008264   0x00000184   Code   RO         5434    i._fp_digits        mc_w.l(printfa.o)
    0x080083e8   0x080083e8   0x000006b4   Code   RO         5435    i._printf_core      mc_w.l(printfa.o)
    0x08008a9c   0x08008a9c   0x00000024   Code   RO         5436    i._printf_post_padding  mc_w.l(printfa.o)
    0x08008ac0   0x08008ac0   0x0000002e   Code   RO         5437    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08008aee   0x08008aee   0x00000002   PAD
    0x08008af0   0x08008af0   0x00000050   Code   RO         4551    i.adc_multi_channel_sample  my_adc.o
    0x08008b40   0x08008b40   0x00000022   Code   RO         4552    i.calculate_average  my_adc.o
    0x08008b62   0x08008b62   0x00000014   Code   RO         4553    i.calculate_dc_current  my_adc.o
    0x08008b76   0x08008b76   0x00000014   Code   RO         4554    i.calculate_dc_voltage  my_adc.o
    0x08008b8a   0x08008b8a   0x00000014   Code   RO         4555    i.calculate_rms_current  my_adc.o
    0x08008b9e   0x08008b9e   0x00000014   Code   RO         4556    i.calculate_rms_voltage  my_adc.o
    0x08008bb2   0x08008bb2   0x00000010   Code   RO         4683    i.delay             oled.o
    0x08008bc2   0x08008bc2   0x00000002   PAD
    0x08008bc4   0x08008bc4   0x0000007c   Code   RO         4490    i.display           menu.o
    0x08008c40   0x08008c40   0x00000058   Code   RO         4815    i.display_basic_info  task.o
    0x08008c98   0x08008c98   0x00000058   Code   RO         4816    i.display_dc_basic_info  task.o
    0x08008cf0   0x08008cf0   0x000000b0   Code   RO         4817    i.display_dc_pid_status  task.o
    0x08008da0   0x08008da0   0x0000001c   Code   RO          412    i.fputc             usart.o
    0x08008dbc   0x08008dbc   0x00000040   Code   RO         4818    i.handle_menu_return  task.o
    0x08008dfc   0x08008dfc   0x000000c4   Code   RO         4427    i.key_scan          key.o
    0x08008ec0   0x08008ec0   0x000001a8   Code   RO           15    i.main              main.o
    0x08009068   0x08009068   0x00000034   Code   RO         4557    i.sliding_average_filter  my_adc.o
    0x0800909c   0x0800909c   0x00000018   Code   RO         4558    i.sliding_average_filter_current  my_adc.o
    0x080090b4   0x080090b4   0x00000018   Code   RO         4559    i.sliding_average_filter_dc_current  my_adc.o
    0x080090cc   0x080090cc   0x00000018   Code   RO         4560    i.sliding_average_filter_dc_voltage  my_adc.o
    0x080090e4   0x080090e4   0x00000018   Code   RO         4561    i.sliding_average_filter_voltage  my_adc.o
    0x080090fc   0x080090fc   0x00000054   Code   RO         4819    i.task1             task.o
    0x08009150   0x08009150   0x0000007c   Code   RO         4820    i.task2             task.o
    0x080091cc   0x080091cc   0x000000d8   Code   RO         4821    i.task3             task.o
    0x080092a4   0x080092a4   0x00000038   Code   RO         4822    i.task_init         task.o
    0x080092dc   0x080092dc   0x00000018   Data   RO         3911    .constdata          stm32g4xx_hal_uart.o
    0x080092f4   0x080092f4   0x00000010   Data   RO         4282    .constdata          stm32g4xx_hal_uart_ex.o
    0x08009304   0x08009304   0x00000018   Data   RO         4391    .constdata          system_stm32g4xx.o
    0x0800931c   0x0800931c   0x00000960   Data   RO         4823    .constdata          task.o
    0x08009c7c   0x08009c7c   0x00000056   Data   RO         4492    .conststring        menu.o
    0x08009cd2   0x08009cd2   0x00000002   PAD
    0x08009cd4   0x08009cd4   0x00000020   Data   RO         5497    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08009cf4, Size: 0x00000c18, Max: 0x00008000, ABSOLUTE, COMPRESSED[0x00000060])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000001   Data   RW          414    .data               usart.o
    0x20000001   COMPRESSED   0x00000003   PAD
    0x20000004   COMPRESSED   0x0000000c   Data   RW         1153    .data               stm32g4xx_hal.o
    0x20000010   COMPRESSED   0x00000004   Data   RW         4392    .data               system_stm32g4xx.o
    0x20000014   COMPRESSED   0x00000002   Data   RW         4428    .data               key.o
    0x20000016   COMPRESSED   0x00000002   PAD
    0x20000018   COMPRESSED   0x00000100   Data   RW         4493    .data               menu.o
    0x20000118   COMPRESSED   0x00000034   Data   RW         4563    .data               my_adc.o
    0x2000014c   COMPRESSED   0x00000004   Data   RW         4684    .data               oled.o
    0x20000150   COMPRESSED   0x00000024   Data   RW         4824    .data               task.o
    0x20000174   COMPRESSED   0x0000001c   Data   RW         5034    .data               pid_control.o
    0x20000190   COMPRESSED   0x00000004   Data   RW         5473    .data               mc_w.l(stdout.o)
    0x20000194        -       0x000000cc   Zero   RW          249    .bss                adc.o
    0x20000260        -       0x00000214   Zero   RW          324    .bss                tim.o
    0x20000474        -       0x000000f8   Zero   RW          413    .bss                usart.o
    0x2000056c        -       0x00000168   Zero   RW         4562    .bss                my_adc.o
    0x200006d4        -       0x000000c8   Zero   RW         4920    .bss                pid_yyds.o
    0x2000079c        -       0x00000078   Zero   RW         5033    .bss                pid_control.o
    0x20000814   COMPRESSED   0x00000004   PAD
    0x20000818        -       0x00000400   Zero   RW            1    STACK               startup_stm32g431xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       448         36          0          0        204       1979   adc.o
        80          6          0          0          0        782   dma.o
       316         16          0          0          0       1051   gpio.o
       304         16          0          2          0       2636   key.o
       534        168          0          0          0     675669   main.o
       834         82         86        256          0       3789   menu.o
       810        150          0         52        360       9769   my_adc.o
      1672         94          0          4          0       9466   oled.o
      2826        874          0         28        120      12233   pid_control.o
      1252        270          0          0        200       5379   pid_yyds.o
        36          8        472          0       1024        824   startup_stm32g431xx.o
       194         32          0         12          0       4273   stm32g4xx_hal.o
      3352        140          0          0          0      86978   stm32g4xx_hal_adc.o
       288          8          0          0          0      68215   stm32g4xx_hal_adc_ex.o
       310         22          0          0          0      34299   stm32g4xx_hal_cortex.o
      1090         22          0          0          0       6242   stm32g4xx_hal_dma.o
       486         32          0          0          0       2703   stm32g4xx_hal_gpio.o
        68          6          0          0          0        842   stm32g4xx_hal_msp.o
       300         18          0          0          0       1397   stm32g4xx_hal_pwr_ex.o
      2260        106          0          0          0       7188   stm32g4xx_hal_rcc.o
       880          8          0          0          0       1872   stm32g4xx_hal_rcc_ex.o
      5316        372          0          0          0      28554   stm32g4xx_hal_tim.o
       778         58          0          0          0       8604   stm32g4xx_hal_tim_ex.o
      5008        136         24          0          0      38280   stm32g4xx_hal_uart.o
       360         10         16          0          0       5386   stm32g4xx_hal_uart_ex.o
       138         38          0          0          0       7131   stm32g4xx_it.o
        20          6         24          4          0       1111   system_stm32g4xx.o
      1232        290       2400         36          0       6241   task.o
      1780        108          0          0        532       7642   tim.o
       300         26          0          1        248       2431   usart.o

    ----------------------------------------------------------------------
     33316       <USER>       <GROUP>        400       2692    1042966   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        44          0          2          5          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        86          0          0          0          0          0   __dczerorl2.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0        108   memseta.o
      2218         90          0          0          0        464   printfa.o
         0          0          0          4          0          0   stdout.o
        28          0          0          0          0         76   strcmp.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o

    ----------------------------------------------------------------------
      3808        <USER>          <GROUP>          4          0       1816   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2698        106          0          4          0       1092   mc_w.l
      1104          0          0          0          0        724   mf_w.l

    ----------------------------------------------------------------------
      3808        <USER>          <GROUP>          4          0       1816   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     37124       3264       3056        404       2692    1021282   Grand Totals
     37124       3264       3056         96       2692    1021282   ELF Image Totals (compressed)
     37124       3264       3056         96          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                40180 (  39.24kB)
    Total RW  Size (RW Data + ZI Data)              3096 (   3.02kB)
    Total ROM Size (Code + RO Data + RW Data)      40276 (  39.33kB)

==============================================================================

