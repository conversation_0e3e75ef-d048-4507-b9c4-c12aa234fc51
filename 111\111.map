Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32g431xx.o(RESET) refers to startup_stm32g431xx.o(STACK) for __initial_sp
    startup_stm32g431xx.o(RESET) refers to startup_stm32g431xx.o(.text) for Reset_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.DMA1_Channel1_IRQHandler) for DMA1_Channel1_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.TIM1_BRK_TIM15_IRQHandler) for TIM1_BRK_TIM15_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.TIM6_DAC_IRQHandler) for TIM6_DAC_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(i.TIM7_IRQHandler) for TIM7_IRQHandler
    startup_stm32g431xx.o(.text) refers to system_stm32g4xx.o(i.SystemInit) for SystemInit
    startup_stm32g431xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) for HAL_PWREx_ControlVoltageScaling
    main.o(i.SystemClock_Config) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.SystemClock_Config) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32g4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM6_Init) for MX_TIM6_Init
    main.o(i.main) refers to tim.o(i.MX_TIM7_Init) for MX_TIM7_Init
    main.o(i.main) refers to tim.o(i.MX_TIM15_Init) for MX_TIM15_Init
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.main) refers to menu.o(i.Menu_Init) for Menu_Init
    main.o(i.main) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    main.o(i.main) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    main.o(i.main) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    main.o(i.main) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    main.o(i.main) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) for HAL_TIM_PWM_Start_IT
    main.o(i.main) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) for HAL_TIMEx_PWMN_Start_IT
    main.o(i.main) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) for HAL_TIM_IC_Start_IT
    main.o(i.main) refers to pid_yyds.o(i.PID_Init) for PID_Init
    main.o(i.main) refers to pid_control.o(i.PID_Control_Init) for PID_Control_Init
    main.o(i.main) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.main) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    main.o(i.main) refers to menu.o(i.Menu) for Menu
    main.o(i.main) refers to tim.o(.bss) for htim1
    main.o(i.main) refers to pid_yyds.o(.bss) for SPWM_Depth_PID
    main.o(i.main) refers to task.o(.data) for depth
    main.o(i.main) refers to usart.o(.data) for usart_buff
    main.o(i.main) refers to usart.o(.bss) for huart1
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(i.HAL_ADC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    adc.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.HAL_ADC_MspInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.bss) for hdma_adc1
    adc.o(i.MX_ADC1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.MX_ADC1_Init) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel) for HAL_ADCEx_MultiModeConfigChannel
    adc.o(i.MX_ADC1_Init) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for hadc1
    dma.o(i.MX_DMA_Init) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM15_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM15_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM15_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM15_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init) for HAL_TIM_IC_Init
    tim.o(i.MX_TIM15_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM15_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) for HAL_TIM_IC_ConfigChannel
    tim.o(i.MX_TIM15_Init) refers to tim.o(.bss) for htim15
    tim.o(i.MX_TIM1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableDeadTimePreload) for HAL_TIMEx_EnableDeadTimePreload
    tim.o(i.MX_TIM1_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM1_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for htim1
    tim.o(i.MX_TIM2_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM2_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init) for HAL_TIM_IC_Init
    tim.o(i.MX_TIM2_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) for HAL_TIM_IC_ConfigChannel
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for htim2
    tim.o(i.MX_TIM3_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM3_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for htim3
    tim.o(i.MX_TIM4_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM4_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for htim4
    tim.o(i.MX_TIM6_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM6_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM6_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM6_Init) refers to tim.o(.bss) for htim6
    tim.o(i.MX_TIM7_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM7_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM7_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM7_Init) refers to tim.o(.bss) for htim7
    usart.o(i.HAL_UART_MspDeInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_USART1_UART_Init) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) for HAL_UARTEx_SetTxFifoThreshold
    usart.o(i.MX_USART1_UART_Init) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) for HAL_UARTEx_SetRxFifoThreshold
    usart.o(i.MX_USART1_UART_Init) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode) for HAL_UARTEx_DisableFifoMode
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for huart1
    usart.o(i.fgetc) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Receive) for HAL_UART_Receive
    usart.o(i.fgetc) refers to usart.o(.bss) for huart1
    usart.o(i.fputc) refers to stm32g4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(i.fputc) refers to usart.o(.bss) for huart1
    stm32g4xx_it.o(i.DMA1_Channel1_IRQHandler) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32g4xx_it.o(i.DMA1_Channel1_IRQHandler) refers to adc.o(.bss) for hdma_adc1
    stm32g4xx_it.o(i.SysTick_Handler) refers to stm32g4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32g4xx_it.o(i.TIM1_BRK_TIM15_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32g4xx_it.o(i.TIM1_BRK_TIM15_IRQHandler) refers to tim.o(.bss) for htim1
    stm32g4xx_it.o(i.TIM2_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32g4xx_it.o(i.TIM2_IRQHandler) refers to tim.o(.bss) for htim2
    stm32g4xx_it.o(i.TIM6_DAC_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32g4xx_it.o(i.TIM6_DAC_IRQHandler) refers to tim.o(.bss) for htim6
    stm32g4xx_it.o(i.TIM7_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32g4xx_it.o(i.TIM7_IRQHandler) refers to tim.o(.bss) for htim7
    stm32g4xx_it.o(i.USART1_IRQHandler) refers to stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32g4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32g4xx_hal_msp.o(i.HAL_MspInit) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDDeadBattery) for HAL_PWREx_DisableUCPDDeadBattery
    stm32g4xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsDisableOngoing) for LL_ADC_IsDisableOngoing
    stm32g4xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32g4xx_hal_adc.o(i.ADC_DMAError) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32g4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32g4xx_hal_adc.o(i.ADC_Disable) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsDisableOngoing) for LL_ADC_IsDisableOngoing
    stm32g4xx_hal_adc.o(i.ADC_Disable) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc.o(i.ADC_Disable) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(i.ADC_Enable) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc.o(i.ADC_Enable) refers to stm32g4xx_hal_adc.o(i.LL_ADC_Enable) for LL_ADC_Enable
    stm32g4xx_hal_adc.o(i.ADC_Enable) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(i.ADC_Enable) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32g4xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels) for LL_ADC_SetAnalogWDMonitChannels
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_SetChannelSamplingTime) for LL_ADC_SetChannelSamplingTime
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_SetSamplingTimeCommonConfig) for LL_ADC_SetSamplingTimeCommonConfig
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_GetOffsetChannel) for LL_ADC_GetOffsetChannel
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_SetOffsetState) for LL_ADC_SetOffsetState
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(i.LL_ADC_SetCommonPathInternalCh) for LL_ADC_SetCommonPathInternalCh
    stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode) for LL_ADC_GetMultimode
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback) for HAL_ADCEx_EndOfSamplingCallback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback) for HAL_ADCEx_LevelOutOfWindow2Callback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback) for HAL_ADCEx_LevelOutOfWindow3Callback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback) for HAL_ADCEx_InjectedQueueOverflowCallback
    stm32g4xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32g4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsInternalRegulatorEnabled) for LL_ADC_IsInternalRegulatorEnabled
    stm32g4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_Init) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode) for LL_ADC_GetMultimode
    stm32g4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart) for LL_ADC_REG_IsTriggerSourceSWStart
    stm32g4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode) for LL_ADC_GetMultimode
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32g4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_StartConversion) for LL_ADC_REG_StartConversion
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode) for LL_ADC_GetMultimode
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_StartConversion) for LL_ADC_REG_StartConversion
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode) for LL_ADC_GetMultimode
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32g4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32g4xx_hal_adc.o(i.LL_ADC_REG_StartConversion) for LL_ADC_REG_StartConversion
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVoltageRegulator) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EnterADCDeepPowerDownMode) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetChannelSamplingTime) for LL_ADC_SetChannelSamplingTime
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetSamplingTimeCommonConfig) for LL_ADC_SetSamplingTimeCommonConfig
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_GetOffsetChannel) for LL_ADC_GetOffsetChannel
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetOffsetState) for LL_ADC_SetOffsetState
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetCommonPathInternalCh) for LL_ADC_SetCommonPathInternalCh
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32g4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_GetTrigAuto) for LL_ADC_INJ_GetTrigAuto
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_StartConversion) for LL_ADC_INJ_StartConversion
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32g4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_GetTrigAuto) for LL_ADC_INJ_GetTrigAuto
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_StartConversion) for LL_ADC_INJ_StartConversion
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled) for LL_ADC_IsEnabled
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32g4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32g4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32g4xx_hal.o(i.HAL_DeInit) refers to stm32g4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32g4xx_hal.o(i.HAL_Delay) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal.o(i.HAL_Delay) refers to stm32g4xx_hal.o(.data) for uwTickFreq
    stm32g4xx_hal.o(i.HAL_GetTick) refers to stm32g4xx_hal.o(.data) for uwTick
    stm32g4xx_hal.o(i.HAL_GetTickFreq) refers to stm32g4xx_hal.o(.data) for uwTickFreq
    stm32g4xx_hal.o(i.HAL_GetTickPrio) refers to stm32g4xx_hal.o(.data) for uwTickPrio
    stm32g4xx_hal.o(i.HAL_IncTick) refers to stm32g4xx_hal.o(.data) for uwTick
    stm32g4xx_hal.o(i.HAL_Init) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32g4xx_hal.o(i.HAL_Init) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal.o(i.HAL_Init) refers to stm32g4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32g4xx_hal.o(i.HAL_InitTick) refers to stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32g4xx_hal.o(i.HAL_InitTick) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32g4xx_hal.o(i.HAL_InitTick) refers to stm32g4xx_hal.o(.data) for uwTickFreq
    stm32g4xx_hal.o(i.HAL_InitTick) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal.o(i.HAL_SetTickFreq) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal.o(i.HAL_SetTickFreq) refers to stm32g4xx_hal.o(.data) for uwTickFreq
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal_rcc.o(i.RCC_GetSysClockFreqFromPLLSource) for RCC_GetSysClockFreqFromPLLSource
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32g4xx.o(.constdata) for AHBPrescTable
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal.o(.data) for uwTickPrio
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32g4xx_hal.o(.data) for uwTickPrio
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32g4xx.o(.constdata) for APBPrescTable
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32g4xx.o(.constdata) for APBPrescTable
    stm32g4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32g4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32g4xx_hal.o(.data) for uwTickPrio
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback) for HAL_RCCEx_LSECSS_Callback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_BootLockConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_RDPConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_SecMemConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_WRPConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetPCROP) for FLASH_OB_GetPCROP
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetBootLock) for FLASH_OB_GetBootLock
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetSecMem) for FLASH_OB_GetSecMem
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_WRPConfig) for FLASH_OB_WRPConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_RDPConfig) for FLASH_OB_RDPConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig) for FLASH_OB_PCROPConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_SecMemConfig) for FLASH_OB_SecMemConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_BootLockConfig) for FLASH_OB_BootLockConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32g4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32g4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) for HAL_PWREx_DisableLowPowerRunMode
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode) for HAL_PWREx_EnableLowPowerRunMode
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode) for HAL_PWREx_EnterSTOP1Mode
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode) for HAL_PWREx_EnterSTOP0Mode
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM1Callback) for HAL_PWREx_PVM1Callback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM2Callback) for HAL_PWREx_PVM2Callback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback) for HAL_PWREx_PVM3Callback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback) for HAL_PWREx_PVM4Callback
    stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping) refers to stm32g4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32g4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32g4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to task.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to task.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback) for HAL_TIMEx_Break2Callback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EncoderIndexCallback) for HAL_TIMEx_EncoderIndexCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DirectionChangeCallback) for HAL_TIMEx_DirectionChangeCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_IndexErrorCallback) for HAL_TIMEx_IndexErrorCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_TransitionErrorCallback) for HAL_TIMEx_TransitionErrorCallback
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32g4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to task.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32g4xx_hal_tim.o(i.TIM_DMAError) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to task.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32g4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32g4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32g4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigEncoderIndex) refers to stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ReArmBreakInput) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32g4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32g4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32g4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32g4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32g4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback) for HAL_UARTEx_WakeupCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback) for HAL_UARTEx_TxFifoEmptyCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback) for HAL_UARTEx_RxFifoFullCallback
    stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32g4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32g4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_TxISR_16BIT_FIFOEN) for UART_TxISR_16BIT_FIFOEN
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_TxISR_8BIT_FIFOEN) for UART_TxISR_8BIT_FIFOEN
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_TxISR_16BIT) for UART_TxISR_16BIT
    stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(i.UART_TxISR_8BIT) for UART_TxISR_8BIT
    stm32g4xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.UART_DMAError) refers to stm32g4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32g4xx_hal_uart.o(i.UART_DMAError) refers to stm32g4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32g4xx_hal_uart.o(i.UART_DMAError) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32g4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32g4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32g4xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32g4xx_hal_uart.o(i.UART_SetConfig) refers to stm32g4xx_hal_uart.o(.constdata) for UARTPrescTable
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32g4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) for UART_RxISR_16BIT_FIFOEN
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) for UART_RxISR_8BIT_FIFOEN
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32g4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32g4xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32g4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32g4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_EnableFifoMode) refers to stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) refers to stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) refers to stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32g4xx_hal_uart_ex.o(i.UARTEx_Wakeup_AddressConfig) for UARTEx_Wakeup_AddressConfig
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) refers to stm32g4xx_hal_uart_ex.o(.constdata) for numerator
    system_stm32g4xx.o(i.SystemCoreClockUpdate) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    system_stm32g4xx.o(i.SystemCoreClockUpdate) refers to system_stm32g4xx.o(.constdata) for AHBPrescTable
    key.o(i.Encode_scan) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key.o(i.Encode_scan) refers to stm32g4xx_hal.o(i.HAL_Delay) for HAL_Delay
    key.o(i.key_scan) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key.o(i.key_scan) refers to stm32g4xx_hal.o(i.HAL_Delay) for HAL_Delay
    key.o(i.key_scan) refers to key.o(.data) for key_up
    menu.o(i.Menu) refers to key.o(i.Encode_scan) for Encode_scan
    menu.o(i.Menu) refers to oled.o(i.OLED_Clear) for OLED_Clear
    menu.o(i.Menu) refers to menu.o(i.display) for display
    menu.o(i.Menu) refers to strcmp.o(.text) for strcmp
    menu.o(i.Menu) refers to menu.o(.data) for key_flag
    menu.o(i.Menu_Init) refers to oled.o(i.OLED_Init) for OLED_Init
    menu.o(i.Menu_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    menu.o(i.Menu_Init) refers to oled.o(i.OLED_ColorTurn) for OLED_ColorTurn
    menu.o(i.Menu_Init) refers to oled.o(i.OLED_DisplayTurn) for OLED_DisplayTurn
    menu.o(i.Menu_Init) refers to menu.o(i.display) for display
    menu.o(i.Menu_Init) refers to menu.o(.data) for SelectItem_Current
    menu.o(i.display) refers to oled.o(i.OLED_Display_GB2312_string) for OLED_Display_GB2312_string
    menu.o(i.display) refers to menu.o(i.Compare_Min) for Compare_Min
    menu.o(i.display) refers to menu.o(.data) for SelectItem_Hidden
    menu.o(.data) refers to menu.o(.conststring) for .conststring
    menu.o(.data) refers to task.o(i.task1) for task1
    menu.o(.data) refers to task.o(i.task2) for task2
    menu.o(.data) refers to task.o(i.task3) for task3
    my_adc.o(i.ADC_Collect_All_Channels) refers to my_adc.o(i.ADC_Sample_Channel) for ADC_Sample_Channel
    my_adc.o(i.ADC_Collect_All_Channels) refers to my_adc.o(.constdata) for .constdata
    my_adc.o(i.ADC_Collect_All_Channels) refers to my_adc.o(.data) for adc_raw_data
    my_adc.o(i.ADC_Debug_Print) refers to my_adc.o(i.ADC_Collect_All_Channels) for ADC_Collect_All_Channels
    my_adc.o(i.ADC_Debug_Print) refers to printfa.o(i.__0printf) for __2printf
    my_adc.o(i.ADC_Debug_Print) refers to f2d.o(.text) for __aeabi_f2d
    my_adc.o(i.ADC_Debug_Print) refers to my_adc.o(.data) for adc_raw_data
    my_adc.o(i.ADC_Sample_Channel) refers to memseta.o(.text) for __aeabi_memclr4
    my_adc.o(i.ADC_Sample_Channel) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    my_adc.o(i.ADC_Sample_Channel) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_Start) for HAL_ADC_Start
    my_adc.o(i.ADC_Sample_Channel) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_PollForConversion) for HAL_ADC_PollForConversion
    my_adc.o(i.ADC_Sample_Channel) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_GetValue) for HAL_ADC_GetValue
    my_adc.o(i.ADC_Sample_Channel) refers to stm32g4xx_hal_adc.o(i.HAL_ADC_Stop) for HAL_ADC_Stop
    my_adc.o(i.ADC_Sample_Channel) refers to adc.o(.bss) for hadc1
    my_adc.o(i.ADC_System_Init) refers to stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) for HAL_ADCEx_Calibration_Start
    my_adc.o(i.ADC_System_Init) refers to adc.o(.bss) for hadc1
    my_adc.o(i.ADC_System_Init) refers to my_adc.o(.bss) for filter_buffer_voltage
    my_adc.o(i.ADC_System_Init) refers to my_adc.o(.data) for adc_raw_data
    my_adc.o(i.Calculate_ADC) refers to my_adc.o(i.ADC_Collect_All_Channels) for ADC_Collect_All_Channels
    my_adc.o(i.Calculate_ADC) refers to my_adc.o(i.Process_AC_Data) for Process_AC_Data
    my_adc.o(i.Calculate_All_ADC) refers to my_adc.o(i.ADC_Collect_All_Channels) for ADC_Collect_All_Channels
    my_adc.o(i.Calculate_All_ADC) refers to my_adc.o(i.Process_AC_Data) for Process_AC_Data
    my_adc.o(i.Calculate_All_ADC) refers to my_adc.o(i.Process_DC_Data) for Process_DC_Data
    my_adc.o(i.Calculate_DC_ADC) refers to my_adc.o(i.ADC_Collect_All_Channels) for ADC_Collect_All_Channels
    my_adc.o(i.Calculate_DC_ADC) refers to my_adc.o(i.Process_DC_Data) for Process_DC_Data
    my_adc.o(i.Process_AC_Data) refers to my_adc.o(i.sliding_average_filter_voltage) for sliding_average_filter_voltage
    my_adc.o(i.Process_AC_Data) refers to my_adc.o(i.sliding_average_filter_current) for sliding_average_filter_current
    my_adc.o(i.Process_AC_Data) refers to my_adc.o(.data) for adc_raw_data
    my_adc.o(i.Process_AC_Data) refers to my_adc.o(.bss) for filter_buffer_voltage
    my_adc.o(i.Process_DC_Data) refers to my_adc.o(i.sliding_average_filter_dc_voltage) for sliding_average_filter_dc_voltage
    my_adc.o(i.Process_DC_Data) refers to my_adc.o(i.sliding_average_filter_dc_current) for sliding_average_filter_dc_current
    my_adc.o(i.Process_DC_Data) refers to f2d.o(.text) for __aeabi_f2d
    my_adc.o(i.Process_DC_Data) refers to dadd.o(.text) for __aeabi_dadd
    my_adc.o(i.Process_DC_Data) refers to d2f.o(.text) for __aeabi_d2f
    my_adc.o(i.Process_DC_Data) refers to my_adc.o(.data) for adc_raw_data
    my_adc.o(i.Process_DC_Data) refers to my_adc.o(.bss) for filter_buffer_dc_voltage
    my_adc.o(i.calculate_dc_current) refers to my_adc.o(i.calculate_average) for calculate_average
    my_adc.o(i.calculate_dc_voltage) refers to my_adc.o(i.calculate_average) for calculate_average
    my_adc.o(i.calculate_rms_current) refers to my_adc.o(i.calculate_average) for calculate_average
    my_adc.o(i.calculate_rms_voltage) refers to my_adc.o(i.calculate_average) for calculate_average
    my_adc.o(i.sliding_average_filter_current) refers to my_adc.o(i.sliding_average_filter) for sliding_average_filter
    my_adc.o(i.sliding_average_filter_current) refers to my_adc.o(.data) for ac_current_index
    my_adc.o(i.sliding_average_filter_dc_current) refers to my_adc.o(i.sliding_average_filter) for sliding_average_filter
    my_adc.o(i.sliding_average_filter_dc_current) refers to my_adc.o(.data) for dc_current_index
    my_adc.o(i.sliding_average_filter_dc_voltage) refers to my_adc.o(i.sliding_average_filter) for sliding_average_filter
    my_adc.o(i.sliding_average_filter_dc_voltage) refers to my_adc.o(.data) for dc_voltage_index
    my_adc.o(i.sliding_average_filter_voltage) refers to my_adc.o(i.sliding_average_filter) for sliding_average_filter
    my_adc.o(i.sliding_average_filter_voltage) refers to my_adc.o(.data) for ac_voltage_index
    oled.o(i.Get_data_from_ROM) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled.o(i.Get_data_from_ROM) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    oled.o(i.INXERSR_OLED_ShowNum) refers to oled.o(i.OLED_Display_GB2312_string) for OLED_Display_GB2312_string
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ColorTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_Off) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisplayTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_128x64) refers to oled.o(i.OLED_address) for OLED_address
    oled.o(i.OLED_Display_128x64) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_16x16) refers to oled.o(i.OLED_address) for OLED_address
    oled.o(i.OLED_Display_16x16) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_5x7) refers to oled.o(i.OLED_address) for OLED_address
    oled.o(i.OLED_Display_5x7) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_8x16) refers to oled.o(i.OLED_address) for OLED_address
    oled.o(i.OLED_Display_8x16) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_GB2312_string) refers to oled.o(i.OLED_get_data_from_ROM) for OLED_get_data_from_ROM
    oled.o(i.OLED_Display_GB2312_string) refers to oled.o(i.OLED_Display_16x16) for OLED_Display_16x16
    oled.o(i.OLED_Display_GB2312_string) refers to oled.o(i.OLED_Display_8x16) for OLED_Display_8x16
    oled.o(i.OLED_Display_GB2312_string) refers to oled.o(.data) for fontaddr
    oled.o(i.OLED_Display_string_5x7) refers to oled.o(i.OLED_get_data_from_ROM) for OLED_get_data_from_ROM
    oled.o(i.OLED_Display_string_5x7) refers to oled.o(i.OLED_Display_5x7) for OLED_Display_5x7
    oled.o(i.OLED_Display_string_5x7) refers to oled.o(.data) for fontaddr
    oled.o(i.OLED_Init) refers to stm32g4xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Display_GB2312_string) for OLED_Display_GB2312_string
    oled.o(i.OLED_WR_Byte) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.delay) for delay
    oled.o(i.OLED_address) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_get_data_from_ROM) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled.o(i.OLED_get_data_from_ROM) refers to oled.o(i.Send_Command_to_ROM) for Send_Command_to_ROM
    oled.o(i.OLED_get_data_from_ROM) refers to oled.o(i.Get_data_from_ROM) for Get_data_from_ROM
    oled.o(i.Send_Command_to_ROM) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    task.o(i.HAL_TIM_IC_CaptureCallback) refers to stm32g4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue) for HAL_TIM_ReadCapturedValue
    task.o(i.HAL_TIM_IC_CaptureCallback) refers to task.o(.data) for bing_wang_flag
    task.o(i.HAL_TIM_PeriodElapsedCallback) refers to my_adc.o(i.Calculate_All_ADC) for Calculate_All_ADC
    task.o(i.HAL_TIM_PeriodElapsedCallback) refers to pid_control.o(i.PID_Control_Loop) for PID_Control_Loop
    task.o(i.HAL_TIM_PeriodElapsedCallback) refers to pid_control.o(i.DC_PID_Control_Loop) for DC_PID_Control_Loop
    task.o(i.HAL_TIM_PeriodElapsedCallback) refers to task.o(.data) for depth
    task.o(i.HAL_TIM_PeriodElapsedCallback) refers to task.o(.constdata) for sin_value
    task.o(i.dc_task_init) refers to pid_control.o(i.DC_PID_Control_Init) for DC_PID_Control_Init
    task.o(i.dc_task_init) refers to printfa.o(i.__0printf) for __2printf
    task.o(i.dc_task_loop) refers to my_adc.o(i.Calculate_DC_ADC) for Calculate_DC_ADC
    task.o(i.dc_task_loop) refers to pid_control.o(i.DC_PID_Control_Loop) for DC_PID_Control_Loop
    task.o(i.dc_task_status) refers to pid_control.o(i.DC_Get_PID_Status) for DC_Get_PID_Status
    task.o(i.dc_task_status) refers to printfa.o(i.__0printf) for __2printf
    task.o(i.dc_task_status) refers to f2d.o(.text) for __aeabi_f2d
    task.o(i.dc_task_status) refers to my_adc.o(.data) for DC_voltage_out
    task.o(i.display_basic_info) refers to oled.o(i.OLED_Display_GB2312_string) for OLED_Display_GB2312_string
    task.o(i.display_basic_info) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    task.o(i.display_basic_info) refers to my_adc.o(.data) for AC_voltage_out
    task.o(i.display_dc_basic_info) refers to oled.o(i.OLED_Display_GB2312_string) for OLED_Display_GB2312_string
    task.o(i.display_dc_basic_info) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    task.o(i.display_dc_basic_info) refers to my_adc.o(.data) for DC_voltage_out
    task.o(i.display_dc_pid_status) refers to pid_control.o(i.DC_Get_PID_Status) for DC_Get_PID_Status
    task.o(i.display_dc_pid_status) refers to oled.o(i.OLED_Display_GB2312_string) for OLED_Display_GB2312_string
    task.o(i.handle_menu_return) refers to pid_control.o(i.PID_Control_Disable_All) for PID_Control_Disable_All
    task.o(i.handle_menu_return) refers to oled.o(i.OLED_Clear) for OLED_Clear
    task.o(i.handle_menu_return) refers to menu.o(i.display) for display
    task.o(i.handle_menu_return) refers to task.o(.data) for key_num
    task.o(i.handle_menu_return) refers to menu.o(.data) for key_flag
    task.o(i.task1) refers to task.o(i.task_init) for task_init
    task.o(i.task1) refers to task.o(i.display_dc_basic_info) for display_dc_basic_info
    task.o(i.task1) refers to task.o(i.display_dc_pid_status) for display_dc_pid_status
    task.o(i.task1) refers to printfa.o(i.__0printf) for __2printf
    task.o(i.task1) refers to pid_control.o(i.DC_Enable_Constant_Current) for DC_Enable_Constant_Current
    task.o(i.task1) refers to pid_control.o(i.DC_Disable_PID_Control) for DC_Disable_PID_Control
    task.o(i.task1) refers to task.o(i.handle_menu_return) for handle_menu_return
    task.o(i.task1) refers to task.o(.data) for key_num
    task.o(i.task2) refers to task.o(i.task_init) for task_init
    task.o(i.task2) refers to task.o(i.display_basic_info) for display_basic_info
    task.o(i.task2) refers to oled.o(i.OLED_Display_GB2312_string) for OLED_Display_GB2312_string
    task.o(i.task2) refers to pid_control.o(i.PID_Control_Enable_Voltage) for PID_Control_Enable_Voltage
    task.o(i.task2) refers to pid_control.o(i.PID_Control_Disable_All) for PID_Control_Disable_All
    task.o(i.task2) refers to task.o(i.handle_menu_return) for handle_menu_return
    task.o(i.task2) refers to pid_control.o(.data) for voltage_control_flag
    task.o(i.task2) refers to task.o(.data) for key_num
    task.o(i.task3) refers to task.o(i.task_init) for task_init
    task.o(i.task3) refers to task.o(i.display_basic_info) for display_basic_info
    task.o(i.task3) refers to oled.o(i.OLED_Display_GB2312_string) for OLED_Display_GB2312_string
    task.o(i.task3) refers to pid_control.o(i.PID_Control_Enable_Voltage) for PID_Control_Enable_Voltage
    task.o(i.task3) refers to pid_control.o(i.PID_Control_Disable_All) for PID_Control_Disable_All
    task.o(i.task3) refers to pid_control.o(i.PID_Control_Enable_Current) for PID_Control_Enable_Current
    task.o(i.task3) refers to task.o(i.handle_menu_return) for handle_menu_return
    task.o(i.task3) refers to task.o(.data) for bing_wang_flag
    task.o(i.task3) refers to pid_control.o(.data) for current_control_flag
    task.o(i.task_init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    task.o(i.task_init) refers to key.o(i.key_scan) for key_scan
    task.o(i.task_init) refers to task.o(.data) for oled_flag
    task.o(i.task_init) refers to menu.o(.data) for key_flag
    pid_yyds.o(i.PID_Debug_Info) refers to printfa.o(i.__0printf) for __2printf
    pid_yyds.o(i.PID_Debug_Info) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.DC_Current_PID_Calculate) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.DC_Current_PID_Calculate) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.DC_Current_PID_Calculate) refers to pid_control.o(.bss) for dc_current_pid
    pid_control.o(i.DC_Current_PID_Calculate) refers to pid_control.o(.data) for calc_debug_count
    pid_control.o(i.DC_Current_PID_Init) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.DC_Current_PID_Init) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.DC_Current_PID_Init) refers to pid_control.o(.bss) for dc_current_pid
    pid_control.o(i.DC_Current_PID_Reset) refers to pid_control.o(.bss) for dc_current_pid
    pid_control.o(i.DC_Disable_PID_Control) refers to pid_control.o(.bss) for dc_current_pid
    pid_control.o(i.DC_Enable_Constant_Current) refers to pid_control.o(i.DC_Current_PID_Reset) for DC_Current_PID_Reset
    pid_control.o(i.DC_Enable_Constant_Current) refers to pid_control.o(i.DC_Set_PWM_Output) for DC_Set_PWM_Output
    pid_control.o(i.DC_Enable_Constant_Current) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.DC_Enable_Constant_Current) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.DC_Enable_Constant_Current) refers to pid_control.o(.bss) for dc_current_pid
    pid_control.o(i.DC_Enable_Constant_Current) refers to my_adc.o(.data) for DC_current_out
    pid_control.o(i.DC_Get_PID_Status) refers to pid_control.o(.bss) for dc_current_pid
    pid_control.o(i.DC_Get_PID_Status) refers to my_adc.o(.data) for DC_current_out
    pid_control.o(i.DC_Get_PID_Status) refers to pid_control.o(.data) for dc_pwm_output
    pid_control.o(i.DC_PID_Control_Init) refers to pid_control.o(i.DC_Current_PID_Init) for DC_Current_PID_Init
    pid_control.o(i.DC_PID_Control_Loop) refers to pid_control.o(i.DC_Current_PID_Calculate) for DC_Current_PID_Calculate
    pid_control.o(i.DC_PID_Control_Loop) refers to pid_control.o(i.DC_Set_PWM_Output) for DC_Set_PWM_Output
    pid_control.o(i.DC_PID_Control_Loop) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.DC_PID_Control_Loop) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.DC_PID_Control_Loop) refers to pid_control.o(.bss) for dc_current_pid
    pid_control.o(i.DC_PID_Control_Loop) refers to my_adc.o(.data) for DC_current_out
    pid_control.o(i.DC_PID_Control_Loop) refers to pid_control.o(.data) for debug_count
    pid_control.o(i.DC_PID_Reset_All) refers to pid_control.o(i.DC_Current_PID_Reset) for DC_Current_PID_Reset
    pid_control.o(i.DC_PID_Reset_All) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.DC_Set_PID_Parameters) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.DC_Set_PID_Parameters) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.DC_Set_PID_Parameters) refers to pid_control.o(.bss) for dc_current_pid
    pid_control.o(i.DC_Set_PWM_Output) refers to tim.o(.bss) for htim1
    pid_control.o(i.DC_Set_PWM_Output) refers to pid_control.o(.data) for dc_pwm_output
    pid_control.o(i.PID_Control_Check_Stuck) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Check_Stuck) refers to pid_yyds.o(.bss) for SPWM_Depth_PID
    pid_control.o(i.PID_Control_Current_Process) refers to pid_yyds.o(i.PID_Current_Control) for PID_Current_Control
    pid_control.o(i.PID_Control_Current_Process) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.PID_Control_Current_Process) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Current_Process) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Current_Process) refers to task.o(.data) for depth
    pid_control.o(i.PID_Control_Current_Process) refers to my_adc.o(.data) for AC_current_out
    pid_control.o(i.PID_Control_Current_Process) refers to pid_yyds.o(.bss) for Current_Control_PID
    pid_control.o(i.PID_Control_Disable_All) refers to pid_control.o(i.PID_Control_Set_Mode) for PID_Control_Set_Mode
    pid_control.o(i.PID_Control_Enable_Current) refers to pid_control.o(i.PID_Control_Set_Mode) for PID_Control_Set_Mode
    pid_control.o(i.PID_Control_Enable_Current) refers to pid_yyds.o(i.PID_Reset) for PID_Reset
    pid_control.o(i.PID_Control_Enable_Current) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.PID_Control_Enable_Current) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Enable_Current) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Enable_Current) refers to pid_control.o(.data) for target_current
    pid_control.o(i.PID_Control_Enable_Current) refers to pid_yyds.o(.bss) for Current_Control_PID
    pid_control.o(i.PID_Control_Enable_Voltage) refers to pid_control.o(i.PID_Control_Set_Mode) for PID_Control_Set_Mode
    pid_control.o(i.PID_Control_Enable_Voltage) refers to pid_yyds.o(i.PID_Reset) for PID_Reset
    pid_control.o(i.PID_Control_Enable_Voltage) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.PID_Control_Enable_Voltage) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Enable_Voltage) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Enable_Voltage) refers to pid_yyds.o(.bss) for SPWM_Depth_PID
    pid_control.o(i.PID_Control_Get_Mode) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Get_Status) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Get_Status) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.PID_Control_Get_Status) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Get_Status) refers to my_adc.o(.data) for AC_voltage_out
    pid_control.o(i.PID_Control_Get_Status) refers to pid_yyds.o(.bss) for SPWM_Depth_PID
    pid_control.o(i.PID_Control_Get_Status) refers to task.o(.data) for depth
    pid_control.o(i.PID_Control_Init) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Init) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Init) refers to pid_control.o(.data) for voltage_control_flag
    pid_control.o(i.PID_Control_Loop) refers to pid_control.o(i.PID_Control_Voltage_Process) for PID_Control_Voltage_Process
    pid_control.o(i.PID_Control_Loop) refers to pid_control.o(i.PID_Control_Current_Process) for PID_Control_Current_Process
    pid_control.o(i.PID_Control_Loop) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Reset) refers to pid_yyds.o(i.PID_Reset) for PID_Reset
    pid_control.o(i.PID_Control_Reset) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Reset) refers to pid_yyds.o(.bss) for SPWM_Depth_PID
    pid_control.o(i.PID_Control_Reset) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Set_Mode) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Set_Mode) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Set_Mode) refers to pid_control.o(.data) for voltage_control_flag
    pid_control.o(i.PID_Control_Set_Target_Current) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.PID_Control_Set_Target_Current) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Set_Target_Current) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Set_Target_Current) refers to pid_control.o(.data) for target_current
    pid_control.o(i.PID_Control_Set_Target_Current) refers to pid_yyds.o(.bss) for Current_Control_PID
    pid_control.o(i.PID_Control_Set_Target_Voltage) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.PID_Control_Set_Target_Voltage) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Set_Target_Voltage) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Set_Target_Voltage) refers to pid_yyds.o(.bss) for SPWM_Depth_PID
    pid_control.o(i.PID_Control_Voltage_Process) refers to pid_yyds.o(i.PID_SPWM_Depth_Control) for PID_SPWM_Depth_Control
    pid_control.o(i.PID_Control_Voltage_Process) refers to f2d.o(.text) for __aeabi_f2d
    pid_control.o(i.PID_Control_Voltage_Process) refers to printfa.o(i.__0printf) for __2printf
    pid_control.o(i.PID_Control_Voltage_Process) refers to pid_yyds.o(i.PID_Debug_Info) for PID_Debug_Info
    pid_control.o(i.PID_Control_Voltage_Process) refers to pid_control.o(.bss) for pid_control_state
    pid_control.o(i.PID_Control_Voltage_Process) refers to task.o(.data) for depth
    pid_control.o(i.PID_Control_Voltage_Process) refers to my_adc.o(.data) for AC_voltage_out
    pid_control.o(i.PID_Control_Voltage_Process) refers to pid_yyds.o(.bss) for SPWM_Depth_PID
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32g431xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32g431xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32g431xx.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.data), (10 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (48 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (204 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (48 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (52 bytes).
    Removing usart.o(i.fgetc), (32 bytes).
    Removing stm32g4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_adc.o(i.ADC_DMAConvCplt), (146 bytes).
    Removing stm32g4xx_hal_adc.o(i.ADC_DMAError), (30 bytes).
    Removing stm32g4xx_hal_adc.o(i.ADC_DMAHalfConvCplt), (14 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (824 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_DeInit), (452 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_ErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_GetError), (6 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_GetState), (6 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_IRQHandler), (816 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (258 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_StartSampling), (18 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_Start_DMA), (276 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_Start_IT), (468 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_StopSampling), (18 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (136 bytes).
    Removing stm32g4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (86 bytes).
    Removing stm32g4xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels), (48 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_GetValue), (28 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue), (138 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start), (176 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue), (60 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVoltageRegulator), (40 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue), (58 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback), (2 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_EnterADCDeepPowerDownMode), (44 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (1580 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (62 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (320 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback), (2 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (264 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (348 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (92 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (104 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback), (2 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback), (2 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (260 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (292 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA), (292 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop), (100 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA), (152 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT), (112 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_GetOffsetChannel), (18 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_GetTrigAuto), (10 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing), (10 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_INJ_StartConversion), (20 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetChannelSamplingTime), (40 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetCommonPathInternalCh), (12 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetOffsetState), (22 bytes).
    Removing stm32g4xx_hal_adc_ex.o(i.LL_ADC_SetSamplingTimeCommonConfig), (12 bytes).
    Removing stm32g4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DeInit), (60 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32g4xx_hal.o(i.HAL_ResumeTick), (18 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_CCMSRAMErase), (28 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_CCMSRAM_WriteProtectionEnable), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableIOSwitchBooster), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableIOSwitchVDD), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableMemorySwappingBank), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableVREFBUF), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableIOSwitchBooster), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableIOSwitchVDD), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableMemorySwappingBank), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF), (56 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_VREFBUF_TrimmingConfig), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SetTickFreq), (48 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SuspendTick), (18 bytes).
    Removing stm32g4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit), (220 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_DisableLSECSS), (24 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (20 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_EnableLSECSS), (24 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (68 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (272 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (108 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (36 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig), (84 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSGetSynchronizationInfo), (44 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (20 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization), (196 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler), (144 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO), (112 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSECSS), (32 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO), (180 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS), (24 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS_IT), (64 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (176 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (1560 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler), (36 bytes).
    Removing stm32g4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (40 bytes).
    Removing stm32g4xx_hal_flash.o(i.FLASH_Program_Fast), (64 bytes).
    Removing stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (140 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (344 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Lock), (32 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (28 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (32 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (48 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Program), (192 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (184 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Unlock), (48 bytes).
    Removing stm32g4xx_hal_flash.o(.data), (32 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (120 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_BootLockConfig), (60 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetBootLock), (16 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetPCROP), (64 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (28 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetSecMem), (20 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (56 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig), (124 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_RDPConfig), (60 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_SecMemConfig), (68 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (316 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_WRPConfig), (80 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase), (44 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_DisableDebugger), (20 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_EnableDebugger), (20 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_EnableSecMemProtection), (24 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (256 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (192 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (116 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (176 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_DisableRunPowerDown), (40 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_EnableRunPowerDown), (40 bytes).
    Removing stm32g4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (300 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (28 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (46 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (20 bytes).
    Removing stm32g4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (148 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (36 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (36 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (192 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GetPending), (44 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (56 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (24 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (228 bytes).
    Removing stm32g4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_dma.o(i.DMA_SetConfig), (64 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit), (188 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_GetError), (6 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_GetState), (8 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (328 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (90 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_Start), (114 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT), (194 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (106 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxRequestGenerator), (86 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxSync), (88 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_DisableMuxRequestGenerator), (32 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_EnableMuxRequestGenerator), (32 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_MUX_IRQHandler), (102 bytes).
    Removing stm32g4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (184 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DeInit), (28 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (40 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (80 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (44 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (28 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigPVM), (668 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBatteryCharging), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullDown), (132 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullUp), (132 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableInternalWakeUpLine), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode), (84 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM1), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM2), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM3), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM4), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePullUpPullDownConfig), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableSRAM2ContentRetention), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDStandbyMode), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBatteryCharging), (32 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullDown), (208 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullUp), (208 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableInternalWakeUpLine), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM1), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM2), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM3), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM4), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePullUpPullDownConfig), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableSRAM2ContentRetention), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUCPDDeadBattery), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUCPDStandbyMode), (20 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSHUTDOWNMode), (44 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode), (60 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode), (64 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (52 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler), (128 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM1Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM2Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback), (2 bytes).
    Removing stm32g4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (92 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (28 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_Enable), (48 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (28 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (36 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (68 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (48 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (48 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (148 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (36 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (68 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (40 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (128 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (236 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (50 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (70 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (62 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (668 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (22 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (476 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (476 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (42 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (140 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (42 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (140 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (96 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (556 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (252 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (238 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (304 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (280 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (54 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (6 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (58 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (128 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start), (348 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (640 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (162 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (270 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (246 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (144 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (128 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Init), (110 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start), (332 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (672 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (416 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (228 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (336 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (312 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (320 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (96 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (102 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (176 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (200 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (196 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (220 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (128 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (672 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (228 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (336 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (312 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (108 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (108 bytes).
    Removing stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMACaptureCplt), (122 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (64 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (104 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (64 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMAError), (94 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (26 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (14 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMATriggerCplt), (26 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (14 bytes).
    Removing stm32g4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (260 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigAsymmetricalDeadTime), (20 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakInput), (224 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (180 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (208 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (180 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigDeadTime), (20 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigEncoderIndex), (92 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigSlaveModePreload), (20 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableAsymmetricalDeadTime), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableDeadTimePreload), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableEncoderFirstIndex), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableEncoderIndex), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisableSlaveModePreload), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DisarmBreakInput), (94 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DitheringDisable), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DitheringEnable), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableAsymmetricalDeadTime), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableEncoderFirstIndex), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableEncoderIndex), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableSlaveModePreload), (18 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (38 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_GroupChannel5), (72 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (96 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (248 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (204 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (268 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (216 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (78 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (84 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (90 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (232 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (552 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (328 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (136 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (242 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (248 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OC_ConfigPulseOnCompare), (80 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (118 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (142 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (138 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (162 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (232 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (552 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (136 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (242 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (248 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ReArmBreakInput), (152 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (52 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_TISelection), (180 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (20 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (20 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (104 bytes).
    Removing stm32g4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (86 bytes).
    Removing stm32g4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (102 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (102 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (124 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_LIN_Init), (160 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_LIN_SendBreak), (62 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode), (72 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode), (72 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (14 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (148 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Abort), (336 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive), (224 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (256 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (176 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (200 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Abort_IT), (396 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DMAPause), (164 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DMAResume), (148 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DMAStop), (190 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DeInit), (76 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_DisableReceiverTimeout), (88 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_EnableReceiverTimeout), (88 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_GetError), (8 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_GetState), (16 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Receive), (274 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (92 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_ReceiverTimeout_Config), (28 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (160 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (172 bytes).
    Removing stm32g4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMAError), (94 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMAReceiveCplt), (180 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMARxAbortCallback), (82 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMARxHalfCplt), (38 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (48 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMATransmitCplt), (86 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMATxAbortCallback), (92 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMATxHalfCplt), (14 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (46 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_EndTxTransfer), (64 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_Start_Receive_DMA), (200 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_TxISR_16BIT), (98 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_TxISR_16BIT_FIFOEN), (130 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_TxISR_8BIT), (92 bytes).
    Removing stm32g4xx_hal_uart.o(i.UART_TxISR_8BIT_FIFOEN), (126 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set), (64 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init), (152 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableStopMode), (64 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_EnableFifoMode), (88 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_EnableStopMode), (64 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_GetRxEventType), (6 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle), (362 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (98 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT), (96 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig), (144 bytes).
    Removing stm32g4xx_hal_uart_ex.o(i.UARTEx_Wakeup_AddressConfig), (38 bytes).
    Removing system_stm32g4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32g4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32g4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32g4xx.o(i.SystemCoreClockUpdate), (156 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing menu.o(.rev16_text), (4 bytes).
    Removing menu.o(.revsh_text), (4 bytes).
    Removing menu.o(.rrx_text), (6 bytes).
    Removing menu.o(.bss), (20 bytes).
    Removing my_adc.o(.rev16_text), (4 bytes).
    Removing my_adc.o(.revsh_text), (4 bytes).
    Removing my_adc.o(.rrx_text), (6 bytes).
    Removing my_adc.o(i.ADC_Debug_Print), (724 bytes).
    Removing my_adc.o(i.ADC_System_Init), (92 bytes).
    Removing my_adc.o(i.Calculate_ADC), (12 bytes).
    Removing my_adc.o(i.Calculate_DC_ADC), (12 bytes).
    Removing my_adc.o(i.calculate_average), (34 bytes).
    Removing my_adc.o(i.calculate_dc_current), (20 bytes).
    Removing my_adc.o(i.calculate_dc_voltage), (20 bytes).
    Removing my_adc.o(i.calculate_rms_current), (20 bytes).
    Removing my_adc.o(i.calculate_rms_voltage), (20 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.INXERSR_OLED_ShowNum), (320 bytes).
    Removing oled.o(i.OLED_DisPlay_Off), (28 bytes).
    Removing oled.o(i.OLED_DisPlay_On), (28 bytes).
    Removing oled.o(i.OLED_Display_128x64), (66 bytes).
    Removing oled.o(i.OLED_Display_5x7), (58 bytes).
    Removing oled.o(i.OLED_Display_string_5x7), (140 bytes).
    Removing task.o(.rev16_text), (4 bytes).
    Removing task.o(.revsh_text), (4 bytes).
    Removing task.o(.rrx_text), (6 bytes).
    Removing task.o(i.dc_task_init), (40 bytes).
    Removing task.o(i.dc_task_loop), (12 bytes).
    Removing task.o(i.dc_task_status), (328 bytes).
    Removing pid_yyds.o(.rev16_text), (4 bytes).
    Removing pid_yyds.o(.revsh_text), (4 bytes).
    Removing pid_yyds.o(.rrx_text), (6 bytes).
    Removing pid_yyds.o(i.PID_Auto_Tune), (344 bytes).
    Removing pid_yyds.o(i.PID_Current_Auto_Tune), (564 bytes).
    Removing pid_yyds.o(i.PID_Incremental), (296 bytes).
    Removing pid_yyds.o(i.PID_Incremental_Advanced), (364 bytes).
    Removing pid_yyds.o(i.PID_Position), (288 bytes).
    Removing pid_yyds.o(i.PID_SPWM_Auto_Tune), (516 bytes).
    Removing pid_control.o(.rev16_text), (4 bytes).
    Removing pid_control.o(.revsh_text), (4 bytes).
    Removing pid_control.o(.rrx_text), (6 bytes).
    Removing pid_control.o(i.DC_Current_PID_Init), (212 bytes).
    Removing pid_control.o(i.DC_PID_Control_Init), (8 bytes).
    Removing pid_control.o(i.DC_PID_Reset_All), (44 bytes).
    Removing pid_control.o(i.DC_Set_PID_Parameters), (152 bytes).
    Removing pid_control.o(i.PID_Control_Check_Stuck), (104 bytes).
    Removing pid_control.o(i.PID_Control_Get_Mode), (12 bytes).
    Removing pid_control.o(i.PID_Control_Get_Status), (524 bytes).
    Removing pid_control.o(i.PID_Control_Reset), (80 bytes).
    Removing pid_control.o(i.PID_Control_Set_Target_Current), (96 bytes).
    Removing pid_control.o(i.PID_Control_Set_Target_Voltage), (88 bytes).

601 unused section(s) (total 55506 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32g4xx_hal_msp.c          0x00000000   Number         0  stm32g4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32g4xx_it.c               0x00000000   Number         0  stm32g4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32g4xx.c           0x00000000   Number         0  system_stm32g4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal.c 0x00000000   Number         0  stm32g4xx_hal.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc.c 0x00000000   Number         0  stm32g4xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc_ex.c 0x00000000   Number         0  stm32g4xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_cortex.c 0x00000000   Number         0  stm32g4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma.c 0x00000000   Number         0  stm32g4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma_ex.c 0x00000000   Number         0  stm32g4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_exti.c 0x00000000   Number         0  stm32g4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash.c 0x00000000   Number         0  stm32g4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ex.c 0x00000000   Number         0  stm32g4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32g4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_gpio.c 0x00000000   Number         0  stm32g4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr.c 0x00000000   Number         0  stm32g4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32g4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc.c 0x00000000   Number         0  stm32g4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32g4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim.c 0x00000000   Number         0  stm32g4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim_ex.c 0x00000000   Number         0  stm32g4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart.c 0x00000000   Number         0  stm32g4xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart_ex.c 0x00000000   Number         0  stm32g4xx_hal_uart_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_adc.c 0x00000000   Number         0  stm32g4xx_ll_adc.o ABSOLUTE
    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\CODE\OLED.c                           0x00000000   Number         0  oled.o ABSOLUTE
    ..\CODE\key.c                            0x00000000   Number         0  key.o ABSOLUTE
    ..\CODE\menu.c                           0x00000000   Number         0  menu.o ABSOLUTE
    ..\CODE\my_adc.c                         0x00000000   Number         0  my_adc.o ABSOLUTE
    ..\CODE\pid_control.c                    0x00000000   Number         0  pid_control.o ABSOLUTE
    ..\CODE\pid_yyds.c                       0x00000000   Number         0  pid_yyds.o ABSOLUTE
    ..\CODE\task.c                           0x00000000   Number         0  task.o ABSOLUTE
    ..\Core\Src\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32g4xx_hal_msp.c          0x00000000   Number         0  stm32g4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32g4xx_it.c               0x00000000   Number         0  stm32g4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32g4xx.c           0x00000000   Number         0  system_stm32g4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal.c 0x00000000   Number         0  stm32g4xx_hal.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_adc.c 0x00000000   Number         0  stm32g4xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_adc_ex.c 0x00000000   Number         0  stm32g4xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cortex.c 0x00000000   Number         0  stm32g4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma.c 0x00000000   Number         0  stm32g4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma_ex.c 0x00000000   Number         0  stm32g4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_exti.c 0x00000000   Number         0  stm32g4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash.c 0x00000000   Number         0  stm32g4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash_ex.c 0x00000000   Number         0  stm32g4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32g4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_gpio.c 0x00000000   Number         0  stm32g4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr.c 0x00000000   Number         0  stm32g4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32g4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc.c 0x00000000   Number         0  stm32g4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32g4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_tim.c 0x00000000   Number         0  stm32g4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_tim_ex.c 0x00000000   Number         0  stm32g4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_uart.c 0x00000000   Number         0  stm32g4xx_hal_uart.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_uart_ex.c 0x00000000   Number         0  stm32g4xx_hal_uart_ex.o ABSOLUTE
    ..\\CODE\\OLED.c                         0x00000000   Number         0  oled.o ABSOLUTE
    ..\\CODE\\key.c                          0x00000000   Number         0  key.o ABSOLUTE
    ..\\CODE\\menu.c                         0x00000000   Number         0  menu.o ABSOLUTE
    ..\\CODE\\my_adc.c                       0x00000000   Number         0  my_adc.o ABSOLUTE
    ..\\CODE\\pid_control.c                  0x00000000   Number         0  pid_control.o ABSOLUTE
    ..\\CODE\\pid_yyds.c                     0x00000000   Number         0  pid_yyds.o ABSOLUTE
    ..\\CODE\\task.c                         0x00000000   Number         0  task.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32g431xx.s                    0x00000000   Number         0  startup_stm32g431xx.o ABSOLUTE
    RESET                                    0x08000000   Section      472  startup_stm32g431xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001d8   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001d8   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001dc   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001e0   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001e0   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001e0   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x080001e8   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x080001ec   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x080001ec   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x080001ec   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080001ec   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001f0   Section       36  startup_stm32g431xx.o(.text)
    $v0                                      0x080001f0   Number         0  startup_stm32g431xx.o(.text)
    .text                                    0x08000214   Section        0  uldiv.o(.text)
    .text                                    0x08000276   Section        0  memseta.o(.text)
    .text                                    0x0800029a   Section        0  strcmp.o(.text)
    .text                                    0x080002b6   Section        0  dadd.o(.text)
    .text                                    0x08000404   Section        0  f2d.o(.text)
    .text                                    0x0800042a   Section        0  d2f.o(.text)
    .text                                    0x08000462   Section        0  uidiv.o(.text)
    .text                                    0x0800048e   Section        0  llshl.o(.text)
    .text                                    0x080004ac   Section        0  llushr.o(.text)
    .text                                    0x080004cc   Section        0  llsshr.o(.text)
    .text                                    0x080004f0   Section        0  fepilogue.o(.text)
    .text                                    0x080004f0   Section        0  iusefp.o(.text)
    .text                                    0x0800055e   Section        0  depilogue.o(.text)
    .text                                    0x08000618   Section        0  dmul.o(.text)
    .text                                    0x080006fc   Section        0  ddiv.o(.text)
    .text                                    0x080007da   Section        0  dfixul.o(.text)
    .text                                    0x0800080c   Section       48  cdrcmple.o(.text)
    .text                                    0x0800083c   Section       36  init.o(.text)
    .text                                    0x08000860   Section        0  __dczerorl2.o(.text)
    i.ADC_Collect_All_Channels               0x080008b8   Section        0  my_adc.o(i.ADC_Collect_All_Channels)
    ADC_Collect_All_Channels                 0x080008b9   Thumb Code   112  my_adc.o(i.ADC_Collect_All_Channels)
    i.ADC_ConversionStop                     0x08000930   Section        0  stm32g4xx_hal_adc.o(i.ADC_ConversionStop)
    i.ADC_Disable                            0x08000a4c   Section        0  stm32g4xx_hal_adc.o(i.ADC_Disable)
    i.ADC_Enable                             0x08000ae0   Section        0  stm32g4xx_hal_adc.o(i.ADC_Enable)
    i.ADC_Sample_Channel                     0x08000ba8   Section        0  my_adc.o(i.ADC_Sample_Channel)
    ADC_Sample_Channel                       0x08000ba9   Thumb Code    96  my_adc.o(i.ADC_Sample_Channel)
    i.BusFault_Handler                       0x08000c0c   Section        0  stm32g4xx_it.o(i.BusFault_Handler)
    i.Calculate_All_ADC                      0x08000c10   Section        0  my_adc.o(i.Calculate_All_ADC)
    i.Compare_Min                            0x08000c20   Section        0  menu.o(i.Compare_Min)
    Compare_Min                              0x08000c21   Thumb Code    14  menu.o(i.Compare_Min)
    i.DC_Current_PID_Calculate               0x08000c30   Section        0  pid_control.o(i.DC_Current_PID_Calculate)
    DC_Current_PID_Calculate                 0x08000c31   Thumb Code   276  pid_control.o(i.DC_Current_PID_Calculate)
    i.DC_Current_PID_Reset                   0x08000d78   Section        0  pid_control.o(i.DC_Current_PID_Reset)
    DC_Current_PID_Reset                     0x08000d79   Thumb Code    28  pid_control.o(i.DC_Current_PID_Reset)
    i.DC_Disable_PID_Control                 0x08000d9c   Section        0  pid_control.o(i.DC_Disable_PID_Control)
    i.DC_Enable_Constant_Current             0x08000da8   Section        0  pid_control.o(i.DC_Enable_Constant_Current)
    i.DC_Get_PID_Status                      0x08000e50   Section        0  pid_control.o(i.DC_Get_PID_Status)
    i.DC_PID_Control_Loop                    0x08000e80   Section        0  pid_control.o(i.DC_PID_Control_Loop)
    i.DC_Set_PWM_Output                      0x08000f38   Section        0  pid_control.o(i.DC_Set_PWM_Output)
    i.DMA1_Channel1_IRQHandler               0x08000f4c   Section        0  stm32g4xx_it.o(i.DMA1_Channel1_IRQHandler)
    i.DMA_CalcDMAMUXChannelBaseAndMask       0x08000f5c   Section        0  stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask)
    DMA_CalcDMAMUXChannelBaseAndMask         0x08000f5d   Thumb Code    58  stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask)
    i.DMA_CalcDMAMUXRequestGenBaseAndMask    0x08000fa0   Section        0  stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask)
    DMA_CalcDMAMUXRequestGenBaseAndMask      0x08000fa1   Thumb Code    32  stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask)
    i.DebugMon_Handler                       0x08000fc4   Section        0  stm32g4xx_it.o(i.DebugMon_Handler)
    i.Encode_scan                            0x08000fc8   Section        0  key.o(i.Encode_scan)
    i.Error_Handler                          0x08001034   Section        0  main.o(i.Error_Handler)
    i.Get_data_from_ROM                      0x0800103c   Section        0  oled.o(i.Get_data_from_ROM)
    i.HAL_ADCEx_MultiModeConfigChannel       0x08001084   Section        0  stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel)
    i.HAL_ADC_ConfigChannel                  0x08001190   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_GetValue                       0x08001698   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_GetValue)
    i.HAL_ADC_Init                           0x080016a0   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_MspInit                        0x080018fc   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_PollForConversion              0x080019c8   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_PollForConversion)
    i.HAL_ADC_Start                          0x08001b00   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_Start)
    i.HAL_ADC_Stop                           0x08001c1c   Section        0  stm32g4xx_hal_adc.o(i.HAL_ADC_Stop)
    i.HAL_DMA_Abort                          0x08001c66   Section        0  stm32g4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08001cdc   Section        0  stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08001d70   Section        0  stm32g4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08001e70   Section        0  stm32g4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_Delay                              0x08001f40   Section        0  stm32g4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08001f68   Section        0  stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08002134   Section        0  stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x08002144   Section        0  stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08002150   Section        0  stm32g4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x0800215c   Section        0  stm32g4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08002174   Section        0  stm32g4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08002194   Section        0  stm32g4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x080021ec   Section        0  stm32g4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08002230   Section        0  stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08002258   Section        0  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x080022d4   Section        0  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_ControlVoltageScaling        0x080022fc   Section        0  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    i.HAL_PWREx_DisableUCPDDeadBattery       0x08002414   Section        0  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDDeadBattery)
    i.HAL_RCCEx_PeriphCLKConfig              0x08002428   Section        0  stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x08002798   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x080029bc   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x080029c8   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x080029ec   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08002a10   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08002aa8   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08003004   Section        0  stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_Break2Callback               0x08003038   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback)
    i.HAL_TIMEx_BreakCallback                0x0800303a   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x0800303c   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x08003040   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_DirectionChangeCallback      0x08003108   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DirectionChangeCallback)
    i.HAL_TIMEx_EnableDeadTimePreload        0x0800310a   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableDeadTimePreload)
    i.HAL_TIMEx_EncoderIndexCallback         0x0800311c   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EncoderIndexCallback)
    i.HAL_TIMEx_IndexErrorCallback           0x0800311e   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_IndexErrorCallback)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08003120   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIMEx_PWMN_Start_IT                0x080031d8   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT)
    i.HAL_TIMEx_TransitionErrorCallback      0x08003320   Section        0  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_TransitionErrorCallback)
    i.HAL_TIM_Base_Init                      0x08003322   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08003390   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start                     0x0800355c   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    i.HAL_TIM_Base_Start_IT                  0x080035ec   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_ConfigClockSource              0x08003688   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_Encoder_Init                   0x080037bc   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x08003888   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_Encoder_Start                  0x08003900   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    i.HAL_TIM_IC_CaptureCallback             0x080039cc   Section        0  task.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IC_ConfigChannel               0x08003a74   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel)
    i.HAL_TIM_IC_Init                        0x08003b52   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init)
    i.HAL_TIM_IC_MspInit                     0x08003bc0   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspInit)
    i.HAL_TIM_IC_Start_IT                    0x08003bc4   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT)
    i.HAL_TIM_IRQHandler                     0x08003d74   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x08003f94   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08004060   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x08004062   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x080041d2   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08004240   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08004242   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x08004244   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PWM_Start_IT                   0x08004390   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT)
    i.HAL_TIM_PeriodElapsedCallback          0x08004530   Section        0  task.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_ReadCapturedValue              0x080045d4   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue)
    i.HAL_TIM_TriggerCallback                0x08004606   Section        0  stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_DisableFifoMode             0x08004608   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode)
    i.HAL_UARTEx_RxEventCallback             0x08004656   Section        0  stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UARTEx_RxFifoFullCallback          0x08004658   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback)
    i.HAL_UARTEx_SetRxFifoThreshold          0x0800465a   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold)
    i.HAL_UARTEx_SetTxFifoThreshold          0x080046b8   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold)
    i.HAL_UARTEx_TxFifoEmptyCallback         0x08004716   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback)
    i.HAL_UARTEx_WakeupCallback              0x08004718   Section        0  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    i.HAL_UART_ErrorCallback                 0x0800471a   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x0800471c   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08004aec   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08004b64   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x08004c08   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08004c64   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_Transmit                      0x08004c66   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08004d28   Section        0  stm32g4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08004d2a   Section        0  stm32g4xx_it.o(i.HardFault_Handler)
    i.LL_ADC_Enable                          0x08004d30   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_Enable)
    LL_ADC_Enable                            0x08004d31   Thumb Code    12  stm32g4xx_hal_adc.o(i.LL_ADC_Enable)
    i.LL_ADC_GetMultimode                    0x08004d40   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode)
    LL_ADC_GetMultimode                      0x08004d41   Thumb Code    10  stm32g4xx_hal_adc.o(i.LL_ADC_GetMultimode)
    i.LL_ADC_GetOffsetChannel                0x08004d4a   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_GetOffsetChannel)
    LL_ADC_GetOffsetChannel                  0x08004d4b   Thumb Code    18  stm32g4xx_hal_adc.o(i.LL_ADC_GetOffsetChannel)
    i.LL_ADC_INJ_IsConversionOngoing         0x08004d5c   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing)
    LL_ADC_INJ_IsConversionOngoing           0x08004d5d   Thumb Code    10  stm32g4xx_hal_adc.o(i.LL_ADC_INJ_IsConversionOngoing)
    i.LL_ADC_IsDisableOngoing                0x08004d66   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_IsDisableOngoing)
    LL_ADC_IsDisableOngoing                  0x08004d67   Thumb Code    10  stm32g4xx_hal_adc.o(i.LL_ADC_IsDisableOngoing)
    i.LL_ADC_IsEnabled                       0x08004d70   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled)
    LL_ADC_IsEnabled                         0x08004d71   Thumb Code    10  stm32g4xx_hal_adc.o(i.LL_ADC_IsEnabled)
    i.LL_ADC_IsEnabled                       0x08004d7a   Section        0  stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled)
    LL_ADC_IsEnabled                         0x08004d7b   Thumb Code    10  stm32g4xx_hal_adc_ex.o(i.LL_ADC_IsEnabled)
    i.LL_ADC_IsInternalRegulatorEnabled      0x08004d84   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_IsInternalRegulatorEnabled)
    LL_ADC_IsInternalRegulatorEnabled        0x08004d85   Thumb Code    10  stm32g4xx_hal_adc.o(i.LL_ADC_IsInternalRegulatorEnabled)
    i.LL_ADC_REG_IsConversionOngoing         0x08004d8e   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing)
    LL_ADC_REG_IsConversionOngoing           0x08004d8f   Thumb Code    10  stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing)
    i.LL_ADC_REG_IsConversionOngoing         0x08004d98   Section        0  stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing)
    LL_ADC_REG_IsConversionOngoing           0x08004d99   Thumb Code    10  stm32g4xx_hal_adc_ex.o(i.LL_ADC_REG_IsConversionOngoing)
    i.LL_ADC_REG_IsTriggerSourceSWStart      0x08004da2   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart)
    LL_ADC_REG_IsTriggerSourceSWStart        0x08004da3   Thumb Code    18  stm32g4xx_hal_adc.o(i.LL_ADC_REG_IsTriggerSourceSWStart)
    i.LL_ADC_REG_StartConversion             0x08004db4   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_REG_StartConversion)
    LL_ADC_REG_StartConversion               0x08004db5   Thumb Code    12  stm32g4xx_hal_adc.o(i.LL_ADC_REG_StartConversion)
    i.LL_ADC_SetChannelSamplingTime          0x08004dc4   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_SetChannelSamplingTime)
    LL_ADC_SetChannelSamplingTime            0x08004dc5   Thumb Code    40  stm32g4xx_hal_adc.o(i.LL_ADC_SetChannelSamplingTime)
    i.LL_ADC_SetCommonPathInternalCh         0x08004dec   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_SetCommonPathInternalCh)
    LL_ADC_SetCommonPathInternalCh           0x08004ded   Thumb Code    12  stm32g4xx_hal_adc.o(i.LL_ADC_SetCommonPathInternalCh)
    i.LL_ADC_SetOffsetState                  0x08004df8   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_SetOffsetState)
    LL_ADC_SetOffsetState                    0x08004df9   Thumb Code    22  stm32g4xx_hal_adc.o(i.LL_ADC_SetOffsetState)
    i.LL_ADC_SetSamplingTimeCommonConfig     0x08004e0e   Section        0  stm32g4xx_hal_adc.o(i.LL_ADC_SetSamplingTimeCommonConfig)
    LL_ADC_SetSamplingTimeCommonConfig       0x08004e0f   Thumb Code    12  stm32g4xx_hal_adc.o(i.LL_ADC_SetSamplingTimeCommonConfig)
    i.MX_ADC1_Init                           0x08004e1c   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_DMA_Init                            0x08004f10   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08004f60   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_TIM15_Init                          0x0800509c   Section        0  tim.o(i.MX_TIM15_Init)
    i.MX_TIM1_Init                           0x08005140   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x0800525c   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x080052fc   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM4_Init                           0x0800536c   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_TIM6_Init                           0x080053e8   Section        0  tim.o(i.MX_TIM6_Init)
    i.MX_TIM7_Init                           0x08005434   Section        0  tim.o(i.MX_TIM7_Init)
    i.MX_USART1_UART_Init                    0x08005480   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MemManage_Handler                      0x080054ec   Section        0  stm32g4xx_it.o(i.MemManage_Handler)
    i.Menu                                   0x080054f0   Section        0  menu.o(i.Menu)
    i.Menu_Init                              0x08005744   Section        0  menu.o(i.Menu_Init)
    i.NMI_Handler                            0x080057a8   Section        0  stm32g4xx_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x080057ac   Section        0  oled.o(i.OLED_Clear)
    i.OLED_ColorTurn                         0x080057ec   Section        0  oled.o(i.OLED_ColorTurn)
    i.OLED_DisplayTurn                       0x0800580a   Section        0  oled.o(i.OLED_DisplayTurn)
    i.OLED_Display_16x16                     0x08005838   Section        0  oled.o(i.OLED_Display_16x16)
    i.OLED_Display_8x16                      0x0800588a   Section        0  oled.o(i.OLED_Display_8x16)
    i.OLED_Display_GB2312_string             0x080058dc   Section        0  oled.o(i.OLED_Display_GB2312_string)
    i.OLED_Init                              0x08005a5c   Section        0  oled.o(i.OLED_Init)
    i.OLED_ShowNum                           0x08005b3c   Section        0  oled.o(i.OLED_ShowNum)
    i.OLED_WR_Byte                           0x08005c7c   Section        0  oled.o(i.OLED_WR_Byte)
    i.OLED_address                           0x08005d08   Section        0  oled.o(i.OLED_address)
    i.OLED_get_data_from_ROM                 0x08005d38   Section        0  oled.o(i.OLED_get_data_from_ROM)
    i.PID_Control_Current_Process            0x08005d90   Section        0  pid_control.o(i.PID_Control_Current_Process)
    PID_Control_Current_Process              0x08005d91   Thumb Code   382  pid_control.o(i.PID_Control_Current_Process)
    i.PID_Control_Disable_All                0x08005fb4   Section        0  pid_control.o(i.PID_Control_Disable_All)
    i.PID_Control_Enable_Current             0x08005fc0   Section        0  pid_control.o(i.PID_Control_Enable_Current)
    i.PID_Control_Enable_Voltage             0x08006034   Section        0  pid_control.o(i.PID_Control_Enable_Voltage)
    i.PID_Control_Init                       0x0800609c   Section        0  pid_control.o(i.PID_Control_Init)
    i.PID_Control_Loop                       0x0800611c   Section        0  pid_control.o(i.PID_Control_Loop)
    i.PID_Control_Set_Mode                   0x08006148   Section        0  pid_control.o(i.PID_Control_Set_Mode)
    i.PID_Control_Voltage_Process            0x08006248   Section        0  pid_control.o(i.PID_Control_Voltage_Process)
    PID_Control_Voltage_Process              0x08006249   Thumb Code   440  pid_control.o(i.PID_Control_Voltage_Process)
    i.PID_Current_Control                    0x080064bc   Section        0  pid_yyds.o(i.PID_Current_Control)
    i.PID_Debug_Info                         0x080065d8   Section        0  pid_yyds.o(i.PID_Debug_Info)
    i.PID_Init                               0x0800680c   Section        0  pid_yyds.o(i.PID_Init)
    i.PID_Reset                              0x08006850   Section        0  pid_yyds.o(i.PID_Reset)
    i.PID_SPWM_Depth_Control                 0x08006884   Section        0  pid_yyds.o(i.PID_SPWM_Depth_Control)
    i.PendSV_Handler                         0x080069a0   Section        0  stm32g4xx_it.o(i.PendSV_Handler)
    i.Process_AC_Data                        0x080069a4   Section        0  my_adc.o(i.Process_AC_Data)
    Process_AC_Data                          0x080069a5   Thumb Code   172  my_adc.o(i.Process_AC_Data)
    i.Process_DC_Data                        0x08006a8c   Section        0  my_adc.o(i.Process_DC_Data)
    Process_DC_Data                          0x08006a8d   Thumb Code   228  my_adc.o(i.Process_DC_Data)
    i.RCC_GetSysClockFreqFromPLLSource       0x08006bb8   Section        0  stm32g4xx_hal_rcc.o(i.RCC_GetSysClockFreqFromPLLSource)
    RCC_GetSysClockFreqFromPLLSource         0x08006bb9   Thumb Code    90  stm32g4xx_hal_rcc.o(i.RCC_GetSysClockFreqFromPLLSource)
    i.SVC_Handler                            0x08006c20   Section        0  stm32g4xx_it.o(i.SVC_Handler)
    i.Send_Command_to_ROM                    0x08006c24   Section        0  oled.o(i.Send_Command_to_ROM)
    i.SysTick_Handler                        0x08006c74   Section        0  stm32g4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08006c7c   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08006ce4   Section        0  system_stm32g4xx.o(i.SystemInit)
    i.TIM1_BRK_TIM15_IRQHandler              0x08006cf8   Section        0  stm32g4xx_it.o(i.TIM1_BRK_TIM15_IRQHandler)
    i.TIM2_IRQHandler                        0x08006d10   Section        0  stm32g4xx_it.o(i.TIM2_IRQHandler)
    i.TIM6_DAC_IRQHandler                    0x08006d20   Section        0  stm32g4xx_it.o(i.TIM6_DAC_IRQHandler)
    i.TIM7_IRQHandler                        0x08006d30   Section        0  stm32g4xx_it.o(i.TIM7_IRQHandler)
    i.TIM_Base_SetConfig                     0x08006d40   Section        0  stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x08006e0c   Section        0  stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_CCxNChannelCmd                     0x08006e2e   Section        0  stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd)
    TIM_CCxNChannelCmd                       0x08006e2f   Thumb Code    34  stm32g4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd)
    i.TIM_ETR_SetConfig                      0x08006e50   Section        0  stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08006e68   Section        0  stm32g4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08006e69   Thumb Code    18  stm32g4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x08006e80   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08006e81   Thumb Code   146  stm32g4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08006f2c   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08006fe0   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08006fe1   Thumb Code   154  stm32g4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08007094   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08007095   Thumb Code   156  stm32g4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_OC5_SetConfig                      0x08007148   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC5_SetConfig)
    TIM_OC5_SetConfig                        0x08007149   Thumb Code    86  stm32g4xx_hal_tim.o(i.TIM_OC5_SetConfig)
    i.TIM_OC6_SetConfig                      0x080071b8   Section        0  stm32g4xx_hal_tim.o(i.TIM_OC6_SetConfig)
    TIM_OC6_SetConfig                        0x080071b9   Thumb Code    88  stm32g4xx_hal_tim.o(i.TIM_OC6_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08007228   Section        0  stm32g4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08007229   Thumb Code    38  stm32g4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI1_SetConfig                      0x08007250   Section        0  stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig)
    i.TIM_TI2_ConfigInputStage               0x080072cc   Section        0  stm32g4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x080072cd   Thumb Code    40  stm32g4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.TIM_TI2_SetConfig                      0x080072f4   Section        0  stm32g4xx_hal_tim.o(i.TIM_TI2_SetConfig)
    TIM_TI2_SetConfig                        0x080072f5   Thumb Code    58  stm32g4xx_hal_tim.o(i.TIM_TI2_SetConfig)
    i.TIM_TI3_SetConfig                      0x0800732e   Section        0  stm32g4xx_hal_tim.o(i.TIM_TI3_SetConfig)
    TIM_TI3_SetConfig                        0x0800732f   Thumb Code    56  stm32g4xx_hal_tim.o(i.TIM_TI3_SetConfig)
    i.TIM_TI4_SetConfig                      0x08007366   Section        0  stm32g4xx_hal_tim.o(i.TIM_TI4_SetConfig)
    TIM_TI4_SetConfig                        0x08007367   Thumb Code    60  stm32g4xx_hal_tim.o(i.TIM_TI4_SetConfig)
    i.UARTEx_SetNbDataToProcess              0x080073a4   Section        0  stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess)
    UARTEx_SetNbDataToProcess                0x080073a5   Thumb Code    78  stm32g4xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess)
    i.UART_AdvFeatureConfig                  0x080073fc   Section        0  stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig)
    i.UART_CheckIdleState                    0x080074f4   Section        0  stm32g4xx_hal_uart.o(i.UART_CheckIdleState)
    i.UART_DMAAbortOnError                   0x080075de   Section        0  stm32g4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x080075df   Thumb Code    20  stm32g4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x080075f4   Section        0  stm32g4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x080075f5   Thumb Code   104  stm32g4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x08007660   Section        0  stm32g4xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x08007661   Thumb Code    48  stm32g4xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_RxISR_16BIT                       0x08007690   Section        0  stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT)
    UART_RxISR_16BIT                         0x08007691   Thumb Code   256  stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT)
    i.UART_RxISR_16BIT_FIFOEN                0x08007794   Section        0  stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN)
    UART_RxISR_16BIT_FIFOEN                  0x08007795   Thumb Code   498  stm32g4xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN)
    i.UART_RxISR_8BIT                        0x08007994   Section        0  stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT)
    UART_RxISR_8BIT                          0x08007995   Thumb Code   254  stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT)
    i.UART_RxISR_8BIT_FIFOEN                 0x08007a98   Section        0  stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN)
    UART_RxISR_8BIT_FIFOEN                   0x08007a99   Thumb Code   496  stm32g4xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN)
    i.UART_SetConfig                         0x08007c94   Section        0  stm32g4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_IT                  0x08008018   Section        0  stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08008174   Section        0  stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08008220   Section        0  stm32g4xx_it.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x08008230   Section        0  stm32g4xx_it.o(i.UsageFault_Handler)
    i.__0printf                              0x08008234   Section        0  printfa.o(i.__0printf)
    i.__NVIC_GetPriorityGrouping             0x08008254   Section        0  stm32g4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    __NVIC_GetPriorityGrouping               0x08008255   Thumb Code    10  stm32g4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    i.__NVIC_SetPriority                     0x08008264   Section        0  stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08008265   Thumb Code    32  stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__scatterload_copy                     0x0800828c   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800829a   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0800829c   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x080082ac   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x080082ad   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08008430   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08008431   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08008ae4   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08008ae5   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08008b08   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08008b09   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i.delay                                  0x08008b36   Section        0  oled.o(i.delay)
    delay                                    0x08008b37   Thumb Code    16  oled.o(i.delay)
    i.display                                0x08008b48   Section        0  menu.o(i.display)
    i.display_basic_info                     0x08008bc4   Section        0  task.o(i.display_basic_info)
    display_basic_info                       0x08008bc5   Thumb Code    64  task.o(i.display_basic_info)
    i.display_dc_basic_info                  0x08008c1c   Section        0  task.o(i.display_dc_basic_info)
    display_dc_basic_info                    0x08008c1d   Thumb Code    64  task.o(i.display_dc_basic_info)
    i.display_dc_pid_status                  0x08008c74   Section        0  task.o(i.display_dc_pid_status)
    display_dc_pid_status                    0x08008c75   Thumb Code    72  task.o(i.display_dc_pid_status)
    i.fputc                                  0x08008cec   Section        0  usart.o(i.fputc)
    i.handle_menu_return                     0x08008d08   Section        0  task.o(i.handle_menu_return)
    handle_menu_return                       0x08008d09   Thumb Code    48  task.o(i.handle_menu_return)
    i.key_scan                               0x08008d48   Section        0  key.o(i.key_scan)
    i.main                                   0x08008e0c   Section        0  main.o(i.main)
    i.sliding_average_filter                 0x08008fb4   Section        0  my_adc.o(i.sliding_average_filter)
    sliding_average_filter                   0x08008fb5   Thumb Code    52  my_adc.o(i.sliding_average_filter)
    i.sliding_average_filter_current         0x08008fe8   Section        0  my_adc.o(i.sliding_average_filter_current)
    i.sliding_average_filter_dc_current      0x08009000   Section        0  my_adc.o(i.sliding_average_filter_dc_current)
    i.sliding_average_filter_dc_voltage      0x08009018   Section        0  my_adc.o(i.sliding_average_filter_dc_voltage)
    i.sliding_average_filter_voltage         0x08009030   Section        0  my_adc.o(i.sliding_average_filter_voltage)
    i.task1                                  0x08009048   Section        0  task.o(i.task1)
    i.task2                                  0x080090a8   Section        0  task.o(i.task2)
    i.task3                                  0x08009124   Section        0  task.o(i.task3)
    i.task_init                              0x080091fc   Section        0  task.o(i.task_init)
    .constdata                               0x08009234   Section       24  stm32g4xx_hal_uart.o(.constdata)
    .constdata                               0x0800924c   Section       16  stm32g4xx_hal_uart_ex.o(.constdata)
    numerator                                0x0800924c   Data           8  stm32g4xx_hal_uart_ex.o(.constdata)
    denominator                              0x08009254   Data           8  stm32g4xx_hal_uart_ex.o(.constdata)
    .constdata                               0x0800925c   Section       24  system_stm32g4xx.o(.constdata)
    .constdata                               0x08009274   Section       16  my_adc.o(.constdata)
    .constdata                               0x08009284   Section     2400  task.o(.constdata)
    sin_value                                0x08009284   Data        2400  task.o(.constdata)
    .conststring                             0x08009be4   Section       86  menu.o(.conststring)
    .data                                    0x20000000   Section        1  usart.o(.data)
    .data                                    0x20000004   Section       12  stm32g4xx_hal.o(.data)
    .data                                    0x20000010   Section        4  system_stm32g4xx.o(.data)
    .data                                    0x20000014   Section        2  key.o(.data)
    key_up                                   0x20000014   Data           2  key.o(.data)
    .data                                    0x20000018   Section      256  menu.o(.data)
    .data                                    0x20000118   Section       60  my_adc.o(.data)
    adc_raw_data                             0x20000118   Data           8  my_adc.o(.data)
    ac_voltage_index                         0x20000150   Data           1  my_adc.o(.data)
    ac_current_index                         0x20000151   Data           1  my_adc.o(.data)
    dc_voltage_index                         0x20000152   Data           1  my_adc.o(.data)
    dc_current_index                         0x20000153   Data           1  my_adc.o(.data)
    .data                                    0x20000154   Section        4  oled.o(.data)
    .data                                    0x20000158   Section       36  task.o(.data)
    IC1Value                                 0x20000170   Data           4  task.o(.data)
    IC2Value                                 0x20000174   Data           4  task.o(.data)
    CaptureNumber                            0x20000178   Data           4  task.o(.data)
    .data                                    0x2000017c   Section       12  pid_control.o(.data)
    calc_debug_count                         0x20000186   Data           1  pid_control.o(.data)
    debug_count                              0x20000187   Data           1  pid_control.o(.data)
    .data                                    0x20000188   Section        4  stdout.o(.data)
    .bss                                     0x2000018c   Section      204  adc.o(.bss)
    .bss                                     0x20000258   Section      532  tim.o(.bss)
    .bss                                     0x2000046c   Section      248  usart.o(.bss)
    .bss                                     0x20000564   Section      240  my_adc.o(.bss)
    adc_buffer                               0x20000564   Data          80  my_adc.o(.bss)
    .bss                                     0x20000654   Section      200  pid_yyds.o(.bss)
    .bss                                     0x2000071c   Section       52  pid_control.o(.bss)
    STACK                                    0x20000750   Section     1024  startup_stm32g431xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001d8   Number         0  startup_stm32g431xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32g431xx.o(RESET)
    __Vectors_End                            0x080001d8   Data           0  startup_stm32g431xx.o(RESET)
    __main                                   0x080001d9   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001d9   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001dd   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001e1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001e1   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001e1   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001e1   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x080001e9   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x080001ed   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x080001ed   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080001f1   Thumb Code     8  startup_stm32g431xx.o(.text)
    ADC1_2_IRQHandler                        0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    COMP1_2_3_IRQHandler                     0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    COMP4_IRQHandler                         0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    CORDIC_IRQHandler                        0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    CRS_IRQHandler                           0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel4_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel5_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel6_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMAMUX_OVR_IRQHandler                    0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI0_IRQHandler                         0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI15_10_IRQHandler                     0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI1_IRQHandler                         0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI2_IRQHandler                         0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI3_IRQHandler                         0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI4_IRQHandler                         0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI9_5_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    FDCAN1_IT0_IRQHandler                    0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    FDCAN1_IT1_IRQHandler                    0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    FLASH_IRQHandler                         0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    FMAC_IRQHandler                          0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    FPU_IRQHandler                           0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C1_ER_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C1_EV_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C2_ER_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C2_EV_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C3_ER_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C3_EV_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    LPTIM1_IRQHandler                        0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    LPUART1_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    PVD_PVM_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    RCC_IRQHandler                           0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    RNG_IRQHandler                           0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    RTC_TAMP_LSECSS_IRQHandler               0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    SAI1_IRQHandler                          0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    SPI1_IRQHandler                          0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    SPI2_IRQHandler                          0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    SPI3_IRQHandler                          0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM1_CC_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM1_TRG_COM_TIM17_IRQHandler            0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM1_UP_TIM16_IRQHandler                 0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM3_IRQHandler                          0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM4_IRQHandler                          0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_BRK_IRQHandler                      0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_CC_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_UP_IRQHandler                       0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    UART4_IRQHandler                         0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    UCPD1_IRQHandler                         0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    USART2_IRQHandler                        0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    USART3_IRQHandler                        0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    USBWakeUp_IRQHandler                     0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    USB_HP_IRQHandler                        0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    USB_LP_IRQHandler                        0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    WWDG_IRQHandler                          0x0800020b   Thumb Code     0  startup_stm32g431xx.o(.text)
    __aeabi_uldivmod                         0x08000215   Thumb Code    98  uldiv.o(.text)
    __aeabi_memset                           0x08000277   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000277   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000277   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000285   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000285   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000285   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000289   Thumb Code    18  memseta.o(.text)
    strcmp                                   0x0800029b   Thumb Code    28  strcmp.o(.text)
    __aeabi_dadd                             0x080002b7   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080003f9   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080003ff   Thumb Code     6  dadd.o(.text)
    __aeabi_f2d                              0x08000405   Thumb Code    38  f2d.o(.text)
    __aeabi_d2f                              0x0800042b   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x08000463   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000463   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x0800048f   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0800048f   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080004ad   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080004ad   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x080004cd   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080004cd   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x080004f1   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x080004f1   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08000503   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x0800055f   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x0800057d   Thumb Code   156  depilogue.o(.text)
    __aeabi_dmul                             0x08000619   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x080006fd   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x080007db   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x0800080d   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x0800083d   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x0800083d   Thumb Code     0  init.o(.text)
    __decompress                             0x08000861   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x08000861   Thumb Code    86  __dczerorl2.o(.text)
    ADC_ConversionStop                       0x08000931   Thumb Code   276  stm32g4xx_hal_adc.o(i.ADC_ConversionStop)
    ADC_Disable                              0x08000a4d   Thumb Code   142  stm32g4xx_hal_adc.o(i.ADC_Disable)
    ADC_Enable                               0x08000ae1   Thumb Code   182  stm32g4xx_hal_adc.o(i.ADC_Enable)
    BusFault_Handler                         0x08000c0d   Thumb Code     4  stm32g4xx_it.o(i.BusFault_Handler)
    Calculate_All_ADC                        0x08000c11   Thumb Code    16  my_adc.o(i.Calculate_All_ADC)
    DC_Disable_PID_Control                   0x08000d9d   Thumb Code     8  pid_control.o(i.DC_Disable_PID_Control)
    DC_Enable_Constant_Current               0x08000da9   Thumb Code    80  pid_control.o(i.DC_Enable_Constant_Current)
    DC_Get_PID_Status                        0x08000e51   Thumb Code    34  pid_control.o(i.DC_Get_PID_Status)
    DC_PID_Control_Loop                      0x08000e81   Thumb Code   132  pid_control.o(i.DC_PID_Control_Loop)
    DC_Set_PWM_Output                        0x08000f39   Thumb Code    12  pid_control.o(i.DC_Set_PWM_Output)
    DMA1_Channel1_IRQHandler                 0x08000f4d   Thumb Code    10  stm32g4xx_it.o(i.DMA1_Channel1_IRQHandler)
    DebugMon_Handler                         0x08000fc5   Thumb Code     2  stm32g4xx_it.o(i.DebugMon_Handler)
    Encode_scan                              0x08000fc9   Thumb Code   104  key.o(i.Encode_scan)
    Error_Handler                            0x08001035   Thumb Code     6  main.o(i.Error_Handler)
    Get_data_from_ROM                        0x0800103d   Thumb Code    62  oled.o(i.Get_data_from_ROM)
    HAL_ADCEx_MultiModeConfigChannel         0x08001085   Thumb Code   260  stm32g4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel)
    HAL_ADC_ConfigChannel                    0x08001191   Thumb Code  1252  stm32g4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_GetValue                         0x08001699   Thumb Code     8  stm32g4xx_hal_adc.o(i.HAL_ADC_GetValue)
    HAL_ADC_Init                             0x080016a1   Thumb Code   576  stm32g4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x080018fd   Thumb Code   190  adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_PollForConversion                0x080019c9   Thumb Code   302  stm32g4xx_hal_adc.o(i.HAL_ADC_PollForConversion)
    HAL_ADC_Start                            0x08001b01   Thumb Code   276  stm32g4xx_hal_adc.o(i.HAL_ADC_Start)
    HAL_ADC_Stop                             0x08001c1d   Thumb Code    74  stm32g4xx_hal_adc.o(i.HAL_ADC_Stop)
    HAL_DMA_Abort                            0x08001c67   Thumb Code   118  stm32g4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08001cdd   Thumb Code   148  stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08001d71   Thumb Code   254  stm32g4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08001e71   Thumb Code   200  stm32g4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_Delay                                0x08001f41   Thumb Code    36  stm32g4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08001f69   Thumb Code   428  stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08002135   Thumb Code    16  stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x08002145   Thumb Code    10  stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08002151   Thumb Code     6  stm32g4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x0800215d   Thumb Code    16  stm32g4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08002175   Thumb Code    30  stm32g4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08002195   Thumb Code    74  stm32g4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x080021ed   Thumb Code    62  stm32g4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08002231   Thumb Code    40  stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08002259   Thumb Code   122  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x080022d5   Thumb Code    32  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_ControlVoltageScaling          0x080022fd   Thumb Code   268  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    HAL_PWREx_DisableUCPDDeadBattery         0x08002415   Thumb Code    14  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDDeadBattery)
    HAL_RCCEx_PeriphCLKConfig                0x08002429   Thumb Code   872  stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x08002799   Thumb Code   522  stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x080029bd   Thumb Code     6  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x080029c9   Thumb Code    26  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x080029ed   Thumb Code    26  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08002a11   Thumb Code   138  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08002aa9   Thumb Code  1360  stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08003005   Thumb Code    52  stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_Break2Callback                 0x08003039   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback)
    HAL_TIMEx_BreakCallback                  0x0800303b   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x0800303d   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_ConfigBreakDeadTime            0x08003041   Thumb Code   192  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_DirectionChangeCallback        0x08003109   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_DirectionChangeCallback)
    HAL_TIMEx_EnableDeadTimePreload          0x0800310b   Thumb Code    18  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EnableDeadTimePreload)
    HAL_TIMEx_EncoderIndexCallback           0x0800311d   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_EncoderIndexCallback)
    HAL_TIMEx_IndexErrorCallback             0x0800311f   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_IndexErrorCallback)
    HAL_TIMEx_MasterConfigSynchronization    0x08003121   Thumb Code   158  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIMEx_PWMN_Start_IT                  0x080031d9   Thumb Code   304  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT)
    HAL_TIMEx_TransitionErrorCallback        0x08003321   Thumb Code     2  stm32g4xx_hal_tim_ex.o(i.HAL_TIMEx_TransitionErrorCallback)
    HAL_TIM_Base_Init                        0x08003323   Thumb Code   110  stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08003391   Thumb Code   430  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start                       0x0800355d   Thumb Code   118  stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    HAL_TIM_Base_Start_IT                    0x080035ed   Thumb Code   130  stm32g4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_ConfigClockSource                0x08003689   Thumb Code   300  stm32g4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x080037bd   Thumb Code   198  stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08003889   Thumb Code   110  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_Encoder_Start                    0x08003901   Thumb Code   204  stm32g4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    HAL_TIM_IC_CaptureCallback               0x080039cd   Thumb Code   136  task.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IC_ConfigChannel                 0x08003a75   Thumb Code   222  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel)
    HAL_TIM_IC_Init                          0x08003b53   Thumb Code   110  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Init)
    HAL_TIM_IC_MspInit                       0x08003bc1   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_MspInit)
    HAL_TIM_IC_Start_IT                      0x08003bc5   Thumb Code   408  stm32g4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT)
    HAL_TIM_IRQHandler                       0x08003d75   Thumb Code   544  stm32g4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x08003f95   Thumb Code   188  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_DelayElapsedCallback          0x08004061   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x08004063   Thumb Code   368  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x080041d3   Thumb Code   110  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08004241   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x08004243   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x08004245   Thumb Code   298  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PWM_Start_IT                     0x08004391   Thumb Code   384  stm32g4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT)
    HAL_TIM_PeriodElapsedCallback            0x08004531   Thumb Code   130  task.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_ReadCapturedValue                0x080045d5   Thumb Code    50  stm32g4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue)
    HAL_TIM_TriggerCallback                  0x08004607   Thumb Code     2  stm32g4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_DisableFifoMode               0x08004609   Thumb Code    78  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode)
    HAL_UARTEx_RxEventCallback               0x08004657   Thumb Code     2  stm32g4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UARTEx_RxFifoFullCallback            0x08004659   Thumb Code     2  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback)
    HAL_UARTEx_SetRxFifoThreshold            0x0800465b   Thumb Code    94  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold)
    HAL_UARTEx_SetTxFifoThreshold            0x080046b9   Thumb Code    94  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold)
    HAL_UARTEx_TxFifoEmptyCallback           0x08004717   Thumb Code     2  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback)
    HAL_UARTEx_WakeupCallback                0x08004719   Thumb Code     2  stm32g4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    HAL_UART_ErrorCallback                   0x0800471b   Thumb Code     2  stm32g4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x0800471d   Thumb Code   962  stm32g4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08004aed   Thumb Code   120  stm32g4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08004b65   Thumb Code   154  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x08004c09   Thumb Code    88  stm32g4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08004c65   Thumb Code     2  stm32g4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_Transmit                        0x08004c67   Thumb Code   194  stm32g4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08004d29   Thumb Code     2  stm32g4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08004d2b   Thumb Code     4  stm32g4xx_it.o(i.HardFault_Handler)
    MX_ADC1_Init                             0x08004e1d   Thumb Code   222  adc.o(i.MX_ADC1_Init)
    MX_DMA_Init                              0x08004f11   Thumb Code    74  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08004f61   Thumb Code   300  gpio.o(i.MX_GPIO_Init)
    MX_TIM15_Init                            0x0800509d   Thumb Code   156  tim.o(i.MX_TIM15_Init)
    MX_TIM1_Init                             0x08005141   Thumb Code   276  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x0800525d   Thumb Code   156  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x080052fd   Thumb Code   104  tim.o(i.MX_TIM3_Init)
    MX_TIM4_Init                             0x0800536d   Thumb Code   116  tim.o(i.MX_TIM4_Init)
    MX_TIM6_Init                             0x080053e9   Thumb Code    68  tim.o(i.MX_TIM6_Init)
    MX_TIM7_Init                             0x08005435   Thumb Code    68  tim.o(i.MX_TIM7_Init)
    MX_USART1_UART_Init                      0x08005481   Thumb Code    98  usart.o(i.MX_USART1_UART_Init)
    MemManage_Handler                        0x080054ed   Thumb Code     4  stm32g4xx_it.o(i.MemManage_Handler)
    Menu                                     0x080054f1   Thumb Code   554  menu.o(i.Menu)
    Menu_Init                                0x08005745   Thumb Code    72  menu.o(i.Menu_Init)
    NMI_Handler                              0x080057a9   Thumb Code     4  stm32g4xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x080057ad   Thumb Code    64  oled.o(i.OLED_Clear)
    OLED_ColorTurn                           0x080057ed   Thumb Code    30  oled.o(i.OLED_ColorTurn)
    OLED_DisplayTurn                         0x0800580b   Thumb Code    46  oled.o(i.OLED_DisplayTurn)
    OLED_Display_16x16                       0x08005839   Thumb Code    82  oled.o(i.OLED_Display_16x16)
    OLED_Display_8x16                        0x0800588b   Thumb Code    82  oled.o(i.OLED_Display_8x16)
    OLED_Display_GB2312_string               0x080058dd   Thumb Code   376  oled.o(i.OLED_Display_GB2312_string)
    OLED_Init                                0x08005a5d   Thumb Code   222  oled.o(i.OLED_Init)
    OLED_ShowNum                             0x08005b3d   Thumb Code   270  oled.o(i.OLED_ShowNum)
    OLED_WR_Byte                             0x08005c7d   Thumb Code   134  oled.o(i.OLED_WR_Byte)
    OLED_address                             0x08005d09   Thumb Code    46  oled.o(i.OLED_address)
    OLED_get_data_from_ROM                   0x08005d39   Thumb Code    84  oled.o(i.OLED_get_data_from_ROM)
    PID_Control_Disable_All                  0x08005fb5   Thumb Code    10  pid_control.o(i.PID_Control_Disable_All)
    PID_Control_Enable_Current               0x08005fc1   Thumb Code    62  pid_control.o(i.PID_Control_Enable_Current)
    PID_Control_Enable_Voltage               0x08006035   Thumb Code    56  pid_control.o(i.PID_Control_Enable_Voltage)
    PID_Control_Init                         0x0800609d   Thumb Code    74  pid_control.o(i.PID_Control_Init)
    PID_Control_Loop                         0x0800611d   Thumb Code    38  pid_control.o(i.PID_Control_Loop)
    PID_Control_Set_Mode                     0x08006149   Thumb Code   136  pid_control.o(i.PID_Control_Set_Mode)
    PID_Current_Control                      0x080064bd   Thumb Code   270  pid_yyds.o(i.PID_Current_Control)
    PID_Debug_Info                           0x080065d9   Thumb Code   330  pid_yyds.o(i.PID_Debug_Info)
    PID_Init                                 0x0800680d   Thumb Code    64  pid_yyds.o(i.PID_Init)
    PID_Reset                                0x08006851   Thumb Code    48  pid_yyds.o(i.PID_Reset)
    PID_SPWM_Depth_Control                   0x08006885   Thumb Code   270  pid_yyds.o(i.PID_SPWM_Depth_Control)
    PendSV_Handler                           0x080069a1   Thumb Code     2  stm32g4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08006c21   Thumb Code     2  stm32g4xx_it.o(i.SVC_Handler)
    Send_Command_to_ROM                      0x08006c25   Thumb Code    74  oled.o(i.Send_Command_to_ROM)
    SysTick_Handler                          0x08006c75   Thumb Code     8  stm32g4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08006c7d   Thumb Code   104  main.o(i.SystemClock_Config)
    SystemInit                               0x08006ce5   Thumb Code    14  system_stm32g4xx.o(i.SystemInit)
    TIM1_BRK_TIM15_IRQHandler                0x08006cf9   Thumb Code    16  stm32g4xx_it.o(i.TIM1_BRK_TIM15_IRQHandler)
    TIM2_IRQHandler                          0x08006d11   Thumb Code    10  stm32g4xx_it.o(i.TIM2_IRQHandler)
    TIM6_DAC_IRQHandler                      0x08006d21   Thumb Code    10  stm32g4xx_it.o(i.TIM6_DAC_IRQHandler)
    TIM7_IRQHandler                          0x08006d31   Thumb Code    10  stm32g4xx_it.o(i.TIM7_IRQHandler)
    TIM_Base_SetConfig                       0x08006d41   Thumb Code   174  stm32g4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x08006e0d   Thumb Code    34  stm32g4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_ETR_SetConfig                        0x08006e51   Thumb Code    22  stm32g4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x08006f2d   Thumb Code   156  stm32g4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    TIM_TI1_SetConfig                        0x08007251   Thumb Code   102  stm32g4xx_hal_tim.o(i.TIM_TI1_SetConfig)
    UART_AdvFeatureConfig                    0x080073fd   Thumb Code   248  stm32g4xx_hal_uart.o(i.UART_AdvFeatureConfig)
    UART_CheckIdleState                      0x080074f5   Thumb Code   234  stm32g4xx_hal_uart.o(i.UART_CheckIdleState)
    UART_SetConfig                           0x08007c95   Thumb Code   858  stm32g4xx_hal_uart.o(i.UART_SetConfig)
    UART_Start_Receive_IT                    0x08008019   Thumb Code   332  stm32g4xx_hal_uart.o(i.UART_Start_Receive_IT)
    UART_WaitOnFlagUntilTimeout              0x08008175   Thumb Code   172  stm32g4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    USART1_IRQHandler                        0x08008221   Thumb Code    10  stm32g4xx_it.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x08008231   Thumb Code     4  stm32g4xx_it.o(i.UsageFault_Handler)
    __0printf                                0x08008235   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x08008235   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x08008235   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x08008235   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x08008235   Thumb Code     0  printfa.o(i.__0printf)
    __scatterload_copy                       0x0800828d   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800829b   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0800829d   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    display                                  0x08008b49   Thumb Code   112  menu.o(i.display)
    fputc                                    0x08008ced   Thumb Code    22  usart.o(i.fputc)
    key_scan                                 0x08008d49   Thumb Code   184  key.o(i.key_scan)
    main                                     0x08008e0d   Thumb Code   256  main.o(i.main)
    sliding_average_filter_current           0x08008fe9   Thumb Code    18  my_adc.o(i.sliding_average_filter_current)
    sliding_average_filter_dc_current        0x08009001   Thumb Code    18  my_adc.o(i.sliding_average_filter_dc_current)
    sliding_average_filter_dc_voltage        0x08009019   Thumb Code    18  my_adc.o(i.sliding_average_filter_dc_voltage)
    sliding_average_filter_voltage           0x08009031   Thumb Code    18  my_adc.o(i.sliding_average_filter_voltage)
    task1                                    0x08009049   Thumb Code    72  task.o(i.task1)
    task2                                    0x080090a9   Thumb Code    92  task.o(i.task2)
    task3                                    0x08009125   Thumb Code   156  task.o(i.task3)
    task_init                                0x080091fd   Thumb Code    40  task.o(i.task_init)
    UARTPrescTable                           0x08009234   Data          24  stm32g4xx_hal_uart.o(.constdata)
    AHBPrescTable                            0x0800925c   Data          16  system_stm32g4xx.o(.constdata)
    APBPrescTable                            0x0800926c   Data           8  system_stm32g4xx.o(.constdata)
    Region$$Table$$Base                      0x08009c3c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08009c5c   Number         0  anon$$obj.o(Region$$Table)
    usart_buff                               0x20000000   Data           1  usart.o(.data)
    uwTick                                   0x20000004   Data           4  stm32g4xx_hal.o(.data)
    uwTickPrio                               0x20000008   Data           4  stm32g4xx_hal.o(.data)
    uwTickFreq                               0x2000000c   Data           4  stm32g4xx_hal.o(.data)
    SystemCoreClock                          0x20000010   Data           4  system_stm32g4xx.o(.data)
    MenuPoint                                0x20000018   Data           4  menu.o(.data)
    SelectItem_Current                       0x2000001c   Data           2  menu.o(.data)
    SelectItem_Hidden                        0x2000001e   Data           2  menu.o(.data)
    SelectItem                               0x20000020   Data           2  menu.o(.data)
    Encoder_Key                              0x20000022   Data           1  menu.o(.data)
    Key                                      0x20000023   Data           1  menu.o(.data)
    key_flag                                 0x20000024   Data           1  menu.o(.data)
    MainMenu                                 0x20000028   Data          40  menu.o(.data)
    Menu0Second                              0x20000050   Data          80  menu.o(.data)
    Menutask1                                0x200000a0   Data          40  menu.o(.data)
    Menutask2                                0x200000c8   Data          40  menu.o(.data)
    Menutask3                                0x200000f0   Data          40  menu.o(.data)
    ac_voltage_raw                           0x20000120   Data           4  my_adc.o(.data)
    ac_current_raw                           0x20000124   Data           4  my_adc.o(.data)
    AC_voltage_out                           0x20000128   Data           4  my_adc.o(.data)
    AC_current_out                           0x2000012c   Data           4  my_adc.o(.data)
    dc_voltage_raw                           0x20000130   Data           4  my_adc.o(.data)
    dc_current_raw                           0x20000134   Data           4  my_adc.o(.data)
    DC_voltage_out                           0x20000138   Data           4  my_adc.o(.data)
    DC_current_out                           0x2000013c   Data           4  my_adc.o(.data)
    voltage_out                              0x20000140   Data           4  my_adc.o(.data)
    current_out                              0x20000144   Data           4  my_adc.o(.data)
    dc_voltage_out                           0x20000148   Data           4  my_adc.o(.data)
    dc_current_out                           0x2000014c   Data           4  my_adc.o(.data)
    fontaddr                                 0x20000154   Data           4  oled.o(.data)
    depth                                    0x20000158   Data           4  task.o(.data)
    balance                                  0x2000015c   Data           4  task.o(.data)
    spwm_cnt                                 0x20000160   Data           4  task.o(.data)
    Frequency                                0x20000164   Data           4  task.o(.data)
    oled_flag                                0x20000168   Data           1  task.o(.data)
    bing_wang_flag                           0x20000169   Data           1  task.o(.data)
    key_num                                  0x2000016a   Data           2  task.o(.data)
    set_current                              0x2000016c   Data           4  task.o(.data)
    voltage_control_flag                     0x2000017c   Data           1  pid_control.o(.data)
    current_control_flag                     0x2000017d   Data           1  pid_control.o(.data)
    target_current                           0x20000180   Data           4  pid_control.o(.data)
    dc_pwm_output                            0x20000184   Data           2  pid_control.o(.data)
    __stdout                                 0x20000188   Data           4  stdout.o(.data)
    hadc1                                    0x2000018c   Data         108  adc.o(.bss)
    hdma_adc1                                0x200001f8   Data          96  adc.o(.bss)
    htim1                                    0x20000258   Data          76  tim.o(.bss)
    htim2                                    0x200002a4   Data          76  tim.o(.bss)
    htim3                                    0x200002f0   Data          76  tim.o(.bss)
    htim4                                    0x2000033c   Data          76  tim.o(.bss)
    htim6                                    0x20000388   Data          76  tim.o(.bss)
    htim7                                    0x200003d4   Data          76  tim.o(.bss)
    htim15                                   0x20000420   Data          76  tim.o(.bss)
    rx_buffer                                0x2000046c   Data         100  usart.o(.bss)
    huart1                                   0x200004d0   Data         148  usart.o(.bss)
    filter_buffer_voltage                    0x200005b4   Data          40  my_adc.o(.bss)
    filter_buffer_current                    0x200005dc   Data          40  my_adc.o(.bss)
    filter_buffer_dc_voltage                 0x20000604   Data          40  my_adc.o(.bss)
    filter_buffer_dc_current                 0x2000062c   Data          40  my_adc.o(.bss)
    AC_Voltage_PID                           0x20000654   Data          40  pid_yyds.o(.bss)
    AC_Current_PID                           0x2000067c   Data          40  pid_yyds.o(.bss)
    AC_Frequency_PID                         0x200006a4   Data          40  pid_yyds.o(.bss)
    SPWM_Depth_PID                           0x200006cc   Data          40  pid_yyds.o(.bss)
    Current_Control_PID                      0x200006f4   Data          40  pid_yyds.o(.bss)
    pid_control_state                        0x2000071c   Data          24  pid_control.o(.bss)
    dc_current_pid                           0x20000734   Data          28  pid_control.o(.bss)
    __initial_sp                             0x20000b50   Data           0  startup_stm32g431xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001d9

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00009de8, Max: 0x00020000, ABSOLUTE, COMPRESSED[0x00009cbc])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00009c5c, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001d8   Data   RO            3    RESET               startup_stm32g431xx.o
    0x080001d8   0x080001d8   0x00000000   Code   RO         5189  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001d8   0x080001d8   0x00000004   Code   RO         5465    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001dc   0x080001dc   0x00000004   Code   RO         5468    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001e0   0x080001e0   0x00000000   Code   RO         5470    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001e0   0x080001e0   0x00000000   Code   RO         5472    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001e0   0x080001e0   0x00000008   Code   RO         5473    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001e8   0x080001e8   0x00000004   Code   RO         5480    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x080001ec   0x080001ec   0x00000000   Code   RO         5475    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x080001ec   0x080001ec   0x00000000   Code   RO         5477    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x080001ec   0x080001ec   0x00000004   Code   RO         5466    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001f0   0x080001f0   0x00000024   Code   RO            4    .text               startup_stm32g431xx.o
    0x08000214   0x08000214   0x00000062   Code   RO         5192    .text               mc_w.l(uldiv.o)
    0x08000276   0x08000276   0x00000024   Code   RO         5194    .text               mc_w.l(memseta.o)
    0x0800029a   0x0800029a   0x0000001c   Code   RO         5196    .text               mc_w.l(strcmp.o)
    0x080002b6   0x080002b6   0x0000014e   Code   RO         5459    .text               mf_w.l(dadd.o)
    0x08000404   0x08000404   0x00000026   Code   RO         5461    .text               mf_w.l(f2d.o)
    0x0800042a   0x0800042a   0x00000038   Code   RO         5463    .text               mf_w.l(d2f.o)
    0x08000462   0x08000462   0x0000002c   Code   RO         5482    .text               mc_w.l(uidiv.o)
    0x0800048e   0x0800048e   0x0000001e   Code   RO         5484    .text               mc_w.l(llshl.o)
    0x080004ac   0x080004ac   0x00000020   Code   RO         5486    .text               mc_w.l(llushr.o)
    0x080004cc   0x080004cc   0x00000024   Code   RO         5488    .text               mc_w.l(llsshr.o)
    0x080004f0   0x080004f0   0x00000000   Code   RO         5490    .text               mc_w.l(iusefp.o)
    0x080004f0   0x080004f0   0x0000006e   Code   RO         5491    .text               mf_w.l(fepilogue.o)
    0x0800055e   0x0800055e   0x000000ba   Code   RO         5493    .text               mf_w.l(depilogue.o)
    0x08000618   0x08000618   0x000000e4   Code   RO         5495    .text               mf_w.l(dmul.o)
    0x080006fc   0x080006fc   0x000000de   Code   RO         5497    .text               mf_w.l(ddiv.o)
    0x080007da   0x080007da   0x00000030   Code   RO         5499    .text               mf_w.l(dfixul.o)
    0x0800080a   0x0800080a   0x00000002   PAD
    0x0800080c   0x0800080c   0x00000030   Code   RO         5501    .text               mf_w.l(cdrcmple.o)
    0x0800083c   0x0800083c   0x00000024   Code   RO         5503    .text               mc_w.l(init.o)
    0x08000860   0x08000860   0x00000056   Code   RO         5513    .text               mc_w.l(__dczerorl2.o)
    0x080008b6   0x080008b6   0x00000002   PAD
    0x080008b8   0x080008b8   0x00000078   Code   RO         4546    i.ADC_Collect_All_Channels  my_adc.o
    0x08000930   0x08000930   0x0000011c   Code   RO          603    i.ADC_ConversionStop  stm32g4xx_hal_adc.o
    0x08000a4c   0x08000a4c   0x00000094   Code   RO          607    i.ADC_Disable       stm32g4xx_hal_adc.o
    0x08000ae0   0x08000ae0   0x000000c8   Code   RO          608    i.ADC_Enable        stm32g4xx_hal_adc.o
    0x08000ba8   0x08000ba8   0x00000064   Code   RO         4548    i.ADC_Sample_Channel  my_adc.o
    0x08000c0c   0x08000c0c   0x00000004   Code   RO          467    i.BusFault_Handler  stm32g4xx_it.o
    0x08000c10   0x08000c10   0x00000010   Code   RO         4551    i.Calculate_All_ADC  my_adc.o
    0x08000c20   0x08000c20   0x0000000e   Code   RO         4487    i.Compare_Min       menu.o
    0x08000c2e   0x08000c2e   0x00000002   PAD
    0x08000c30   0x08000c30   0x00000148   Code   RO         5022    i.DC_Current_PID_Calculate  pid_control.o
    0x08000d78   0x08000d78   0x00000024   Code   RO         5024    i.DC_Current_PID_Reset  pid_control.o
    0x08000d9c   0x08000d9c   0x0000000c   Code   RO         5025    i.DC_Disable_PID_Control  pid_control.o
    0x08000da8   0x08000da8   0x000000a8   Code   RO         5026    i.DC_Enable_Constant_Current  pid_control.o
    0x08000e50   0x08000e50   0x00000030   Code   RO         5027    i.DC_Get_PID_Status  pid_control.o
    0x08000e80   0x08000e80   0x000000b8   Code   RO         5029    i.DC_PID_Control_Loop  pid_control.o
    0x08000f38   0x08000f38   0x00000014   Code   RO         5032    i.DC_Set_PWM_Output  pid_control.o
    0x08000f4c   0x08000f4c   0x00000010   Code   RO          468    i.DMA1_Channel1_IRQHandler  stm32g4xx_it.o
    0x08000f5c   0x08000f5c   0x00000044   Code   RO         2026    i.DMA_CalcDMAMUXChannelBaseAndMask  stm32g4xx_hal_dma.o
    0x08000fa0   0x08000fa0   0x00000024   Code   RO         2027    i.DMA_CalcDMAMUXRequestGenBaseAndMask  stm32g4xx_hal_dma.o
    0x08000fc4   0x08000fc4   0x00000002   Code   RO          469    i.DebugMon_Handler  stm32g4xx_it.o
    0x08000fc6   0x08000fc6   0x00000002   PAD
    0x08000fc8   0x08000fc8   0x0000006c   Code   RO         4426    i.Encode_scan       key.o
    0x08001034   0x08001034   0x00000006   Code   RO           13    i.Error_Handler     main.o
    0x0800103a   0x0800103a   0x00000002   PAD
    0x0800103c   0x0800103c   0x00000048   Code   RO         4680    i.Get_data_from_ROM  oled.o
    0x08001084   0x08001084   0x0000010c   Code   RO          898    i.HAL_ADCEx_MultiModeConfigChannel  stm32g4xx_hal_adc_ex.o
    0x08001190   0x08001190   0x00000508   Code   RO          610    i.HAL_ADC_ConfigChannel  stm32g4xx_hal_adc.o
    0x08001698   0x08001698   0x00000008   Code   RO          617    i.HAL_ADC_GetValue  stm32g4xx_hal_adc.o
    0x080016a0   0x080016a0   0x0000025c   Code   RO          619    i.HAL_ADC_Init      stm32g4xx_hal_adc.o
    0x080018fc   0x080018fc   0x000000cc   Code   RO          247    i.HAL_ADC_MspInit   adc.o
    0x080019c8   0x080019c8   0x00000138   Code   RO          623    i.HAL_ADC_PollForConversion  stm32g4xx_hal_adc.o
    0x08001b00   0x08001b00   0x0000011c   Code   RO          625    i.HAL_ADC_Start     stm32g4xx_hal_adc.o
    0x08001c1c   0x08001c1c   0x0000004a   Code   RO          629    i.HAL_ADC_Stop      stm32g4xx_hal_adc.o
    0x08001c66   0x08001c66   0x00000076   Code   RO         2029    i.HAL_DMA_Abort     stm32g4xx_hal_dma.o
    0x08001cdc   0x08001cdc   0x00000094   Code   RO         2030    i.HAL_DMA_Abort_IT  stm32g4xx_hal_dma.o
    0x08001d70   0x08001d70   0x000000fe   Code   RO         2034    i.HAL_DMA_IRQHandler  stm32g4xx_hal_dma.o
    0x08001e6e   0x08001e6e   0x00000002   PAD
    0x08001e70   0x08001e70   0x000000d0   Code   RO         2035    i.HAL_DMA_Init      stm32g4xx_hal_dma.o
    0x08001f40   0x08001f40   0x00000028   Code   RO         1122    i.HAL_Delay         stm32g4xx_hal.o
    0x08001f68   0x08001f68   0x000001cc   Code   RO         1891    i.HAL_GPIO_Init     stm32g4xx_hal_gpio.o
    0x08002134   0x08002134   0x00000010   Code   RO         1893    i.HAL_GPIO_ReadPin  stm32g4xx_hal_gpio.o
    0x08002144   0x08002144   0x0000000a   Code   RO         1895    i.HAL_GPIO_WritePin  stm32g4xx_hal_gpio.o
    0x0800214e   0x0800214e   0x00000002   PAD
    0x08002150   0x08002150   0x0000000c   Code   RO         1126    i.HAL_GetTick       stm32g4xx_hal.o
    0x0800215c   0x0800215c   0x00000018   Code   RO         1132    i.HAL_IncTick       stm32g4xx_hal.o
    0x08002174   0x08002174   0x0000001e   Code   RO         1133    i.HAL_Init          stm32g4xx_hal.o
    0x08002192   0x08002192   0x00000002   PAD
    0x08002194   0x08002194   0x00000058   Code   RO         1134    i.HAL_InitTick      stm32g4xx_hal.o
    0x080021ec   0x080021ec   0x00000044   Code   RO          579    i.HAL_MspInit       stm32g4xx_hal_msp.o
    0x08002230   0x08002230   0x00000028   Code   RO         2540    i.HAL_NVIC_EnableIRQ  stm32g4xx_hal_cortex.o
    0x08002258   0x08002258   0x0000007a   Code   RO         2546    i.HAL_NVIC_SetPriority  stm32g4xx_hal_cortex.o
    0x080022d2   0x080022d2   0x00000002   PAD
    0x080022d4   0x080022d4   0x00000028   Code   RO         2547    i.HAL_NVIC_SetPriorityGrouping  stm32g4xx_hal_cortex.o
    0x080022fc   0x080022fc   0x00000118   Code   RO         2294    i.HAL_PWREx_ControlVoltageScaling  stm32g4xx_hal_pwr_ex.o
    0x08002414   0x08002414   0x00000014   Code   RO         2306    i.HAL_PWREx_DisableUCPDDeadBattery  stm32g4xx_hal_pwr_ex.o
    0x08002428   0x08002428   0x00000370   Code   RO         1498    i.HAL_RCCEx_PeriphCLKConfig  stm32g4xx_hal_rcc_ex.o
    0x08002798   0x08002798   0x00000224   Code   RO         1368    i.HAL_RCC_ClockConfig  stm32g4xx_hal_rcc.o
    0x080029bc   0x080029bc   0x0000000c   Code   RO         1374    i.HAL_RCC_GetHCLKFreq  stm32g4xx_hal_rcc.o
    0x080029c8   0x080029c8   0x00000024   Code   RO         1376    i.HAL_RCC_GetPCLK1Freq  stm32g4xx_hal_rcc.o
    0x080029ec   0x080029ec   0x00000024   Code   RO         1377    i.HAL_RCC_GetPCLK2Freq  stm32g4xx_hal_rcc.o
    0x08002a10   0x08002a10   0x00000098   Code   RO         1378    i.HAL_RCC_GetSysClockFreq  stm32g4xx_hal_rcc.o
    0x08002aa8   0x08002aa8   0x0000055c   Code   RO         1381    i.HAL_RCC_OscConfig  stm32g4xx_hal_rcc.o
    0x08003004   0x08003004   0x00000034   Code   RO         2551    i.HAL_SYSTICK_Config  stm32g4xx_hal_cortex.o
    0x08003038   0x08003038   0x00000002   Code   RO         3412    i.HAL_TIMEx_Break2Callback  stm32g4xx_hal_tim_ex.o
    0x0800303a   0x0800303a   0x00000002   Code   RO         3413    i.HAL_TIMEx_BreakCallback  stm32g4xx_hal_tim_ex.o
    0x0800303c   0x0800303c   0x00000002   Code   RO         3414    i.HAL_TIMEx_CommutCallback  stm32g4xx_hal_tim_ex.o
    0x0800303e   0x0800303e   0x00000002   PAD
    0x08003040   0x08003040   0x000000c8   Code   RO         3417    i.HAL_TIMEx_ConfigBreakDeadTime  stm32g4xx_hal_tim_ex.o
    0x08003108   0x08003108   0x00000002   Code   RO         3425    i.HAL_TIMEx_DirectionChangeCallback  stm32g4xx_hal_tim_ex.o
    0x0800310a   0x0800310a   0x00000012   Code   RO         3435    i.HAL_TIMEx_EnableDeadTimePreload  stm32g4xx_hal_tim_ex.o
    0x0800311c   0x0800311c   0x00000002   Code   RO         3439    i.HAL_TIMEx_EncoderIndexCallback  stm32g4xx_hal_tim_ex.o
    0x0800311e   0x0800311e   0x00000002   Code   RO         3453    i.HAL_TIMEx_IndexErrorCallback  stm32g4xx_hal_tim_ex.o
    0x08003120   0x08003120   0x000000b8   Code   RO         3454    i.HAL_TIMEx_MasterConfigSynchronization  stm32g4xx_hal_tim_ex.o
    0x080031d8   0x080031d8   0x00000148   Code   RO         3468    i.HAL_TIMEx_PWMN_Start_IT  stm32g4xx_hal_tim_ex.o
    0x08003320   0x08003320   0x00000002   Code   RO         3475    i.HAL_TIMEx_TransitionErrorCallback  stm32g4xx_hal_tim_ex.o
    0x08003322   0x08003322   0x0000006e   Code   RO         2687    i.HAL_TIM_Base_Init  stm32g4xx_hal_tim.o
    0x08003390   0x08003390   0x000001cc   Code   RO          313    i.HAL_TIM_Base_MspInit  tim.o
    0x0800355c   0x0800355c   0x00000090   Code   RO         2690    i.HAL_TIM_Base_Start  stm32g4xx_hal_tim.o
    0x080035ec   0x080035ec   0x0000009c   Code   RO         2692    i.HAL_TIM_Base_Start_IT  stm32g4xx_hal_tim.o
    0x08003688   0x08003688   0x00000134   Code   RO         2696    i.HAL_TIM_ConfigClockSource  stm32g4xx_hal_tim.o
    0x080037bc   0x080037bc   0x000000cc   Code   RO         2708    i.HAL_TIM_Encoder_Init  stm32g4xx_hal_tim.o
    0x08003888   0x08003888   0x00000078   Code   RO          315    i.HAL_TIM_Encoder_MspInit  tim.o
    0x08003900   0x08003900   0x000000cc   Code   RO         2711    i.HAL_TIM_Encoder_Start  stm32g4xx_hal_tim.o
    0x080039cc   0x080039cc   0x000000a8   Code   RO         4826    i.HAL_TIM_IC_CaptureCallback  task.o
    0x08003a74   0x08003a74   0x000000de   Code   RO         2723    i.HAL_TIM_IC_ConfigChannel  stm32g4xx_hal_tim.o
    0x08003b52   0x08003b52   0x0000006e   Code   RO         2726    i.HAL_TIM_IC_Init   stm32g4xx_hal_tim.o
    0x08003bc0   0x08003bc0   0x00000002   Code   RO         2728    i.HAL_TIM_IC_MspInit  stm32g4xx_hal_tim.o
    0x08003bc2   0x08003bc2   0x00000002   PAD
    0x08003bc4   0x08003bc4   0x000001b0   Code   RO         2731    i.HAL_TIM_IC_Start_IT  stm32g4xx_hal_tim.o
    0x08003d74   0x08003d74   0x00000220   Code   RO         2735    i.HAL_TIM_IRQHandler  stm32g4xx_hal_tim.o
    0x08003f94   0x08003f94   0x000000cc   Code   RO          316    i.HAL_TIM_MspPostInit  tim.o
    0x08004060   0x08004060   0x00000002   Code   RO         2738    i.HAL_TIM_OC_DelayElapsedCallback  stm32g4xx_hal_tim.o
    0x08004062   0x08004062   0x00000170   Code   RO         2759    i.HAL_TIM_PWM_ConfigChannel  stm32g4xx_hal_tim.o
    0x080041d2   0x080041d2   0x0000006e   Code   RO         2762    i.HAL_TIM_PWM_Init  stm32g4xx_hal_tim.o
    0x08004240   0x08004240   0x00000002   Code   RO         2764    i.HAL_TIM_PWM_MspInit  stm32g4xx_hal_tim.o
    0x08004242   0x08004242   0x00000002   Code   RO         2765    i.HAL_TIM_PWM_PulseFinishedCallback  stm32g4xx_hal_tim.o
    0x08004244   0x08004244   0x0000014c   Code   RO         2767    i.HAL_TIM_PWM_Start  stm32g4xx_hal_tim.o
    0x08004390   0x08004390   0x000001a0   Code   RO         2769    i.HAL_TIM_PWM_Start_IT  stm32g4xx_hal_tim.o
    0x08004530   0x08004530   0x000000a4   Code   RO         4827    i.HAL_TIM_PeriodElapsedCallback  task.o
    0x080045d4   0x080045d4   0x00000032   Code   RO         2775    i.HAL_TIM_ReadCapturedValue  stm32g4xx_hal_tim.o
    0x08004606   0x08004606   0x00000002   Code   RO         2778    i.HAL_TIM_TriggerCallback  stm32g4xx_hal_tim.o
    0x08004608   0x08004608   0x0000004e   Code   RO         4266    i.HAL_UARTEx_DisableFifoMode  stm32g4xx_hal_uart_ex.o
    0x08004656   0x08004656   0x00000002   Code   RO         3850    i.HAL_UARTEx_RxEventCallback  stm32g4xx_hal_uart.o
    0x08004658   0x08004658   0x00000002   Code   RO         4274    i.HAL_UARTEx_RxFifoFullCallback  stm32g4xx_hal_uart_ex.o
    0x0800465a   0x0800465a   0x0000005e   Code   RO         4275    i.HAL_UARTEx_SetRxFifoThreshold  stm32g4xx_hal_uart_ex.o
    0x080046b8   0x080046b8   0x0000005e   Code   RO         4276    i.HAL_UARTEx_SetTxFifoThreshold  stm32g4xx_hal_uart_ex.o
    0x08004716   0x08004716   0x00000002   Code   RO         4278    i.HAL_UARTEx_TxFifoEmptyCallback  stm32g4xx_hal_uart_ex.o
    0x08004718   0x08004718   0x00000002   Code   RO         4279    i.HAL_UARTEx_WakeupCallback  stm32g4xx_hal_uart_ex.o
    0x0800471a   0x0800471a   0x00000002   Code   RO         3866    i.HAL_UART_ErrorCallback  stm32g4xx_hal_uart.o
    0x0800471c   0x0800471c   0x000003d0   Code   RO         3869    i.HAL_UART_IRQHandler  stm32g4xx_hal_uart.o
    0x08004aec   0x08004aec   0x00000078   Code   RO         3870    i.HAL_UART_Init     stm32g4xx_hal_uart.o
    0x08004b64   0x08004b64   0x000000a4   Code   RO          409    i.HAL_UART_MspInit  usart.o
    0x08004c08   0x08004c08   0x0000005c   Code   RO         3875    i.HAL_UART_Receive_IT  stm32g4xx_hal_uart.o
    0x08004c64   0x08004c64   0x00000002   Code   RO         3877    i.HAL_UART_RxCpltCallback  stm32g4xx_hal_uart.o
    0x08004c66   0x08004c66   0x000000c2   Code   RO         3879    i.HAL_UART_Transmit  stm32g4xx_hal_uart.o
    0x08004d28   0x08004d28   0x00000002   Code   RO         3882    i.HAL_UART_TxCpltCallback  stm32g4xx_hal_uart.o
    0x08004d2a   0x08004d2a   0x00000004   Code   RO          470    i.HardFault_Handler  stm32g4xx_it.o
    0x08004d2e   0x08004d2e   0x00000002   PAD
    0x08004d30   0x08004d30   0x00000010   Code   RO          633    i.LL_ADC_Enable     stm32g4xx_hal_adc.o
    0x08004d40   0x08004d40   0x0000000a   Code   RO          634    i.LL_ADC_GetMultimode  stm32g4xx_hal_adc.o
    0x08004d4a   0x08004d4a   0x00000012   Code   RO          635    i.LL_ADC_GetOffsetChannel  stm32g4xx_hal_adc.o
    0x08004d5c   0x08004d5c   0x0000000a   Code   RO          636    i.LL_ADC_INJ_IsConversionOngoing  stm32g4xx_hal_adc.o
    0x08004d66   0x08004d66   0x0000000a   Code   RO          637    i.LL_ADC_IsDisableOngoing  stm32g4xx_hal_adc.o
    0x08004d70   0x08004d70   0x0000000a   Code   RO          638    i.LL_ADC_IsEnabled  stm32g4xx_hal_adc.o
    0x08004d7a   0x08004d7a   0x0000000a   Code   RO          910    i.LL_ADC_IsEnabled  stm32g4xx_hal_adc_ex.o
    0x08004d84   0x08004d84   0x0000000a   Code   RO          639    i.LL_ADC_IsInternalRegulatorEnabled  stm32g4xx_hal_adc.o
    0x08004d8e   0x08004d8e   0x0000000a   Code   RO          640    i.LL_ADC_REG_IsConversionOngoing  stm32g4xx_hal_adc.o
    0x08004d98   0x08004d98   0x0000000a   Code   RO          911    i.LL_ADC_REG_IsConversionOngoing  stm32g4xx_hal_adc_ex.o
    0x08004da2   0x08004da2   0x00000012   Code   RO          641    i.LL_ADC_REG_IsTriggerSourceSWStart  stm32g4xx_hal_adc.o
    0x08004db4   0x08004db4   0x00000010   Code   RO          642    i.LL_ADC_REG_StartConversion  stm32g4xx_hal_adc.o
    0x08004dc4   0x08004dc4   0x00000028   Code   RO          644    i.LL_ADC_SetChannelSamplingTime  stm32g4xx_hal_adc.o
    0x08004dec   0x08004dec   0x0000000c   Code   RO          645    i.LL_ADC_SetCommonPathInternalCh  stm32g4xx_hal_adc.o
    0x08004df8   0x08004df8   0x00000016   Code   RO          646    i.LL_ADC_SetOffsetState  stm32g4xx_hal_adc.o
    0x08004e0e   0x08004e0e   0x0000000c   Code   RO          647    i.LL_ADC_SetSamplingTimeCommonConfig  stm32g4xx_hal_adc.o
    0x08004e1a   0x08004e1a   0x00000002   PAD
    0x08004e1c   0x08004e1c   0x000000f4   Code   RO          248    i.MX_ADC1_Init      adc.o
    0x08004f10   0x08004f10   0x00000050   Code   RO          288    i.MX_DMA_Init       dma.o
    0x08004f60   0x08004f60   0x0000013c   Code   RO          222    i.MX_GPIO_Init      gpio.o
    0x0800509c   0x0800509c   0x000000a4   Code   RO          317    i.MX_TIM15_Init     tim.o
    0x08005140   0x08005140   0x0000011c   Code   RO          318    i.MX_TIM1_Init      tim.o
    0x0800525c   0x0800525c   0x000000a0   Code   RO          319    i.MX_TIM2_Init      tim.o
    0x080052fc   0x080052fc   0x00000070   Code   RO          320    i.MX_TIM3_Init      tim.o
    0x0800536c   0x0800536c   0x0000007c   Code   RO          321    i.MX_TIM4_Init      tim.o
    0x080053e8   0x080053e8   0x0000004c   Code   RO          322    i.MX_TIM6_Init      tim.o
    0x08005434   0x08005434   0x0000004c   Code   RO          323    i.MX_TIM7_Init      tim.o
    0x08005480   0x08005480   0x0000006c   Code   RO          410    i.MX_USART1_UART_Init  usart.o
    0x080054ec   0x080054ec   0x00000004   Code   RO          471    i.MemManage_Handler  stm32g4xx_it.o
    0x080054f0   0x080054f0   0x00000254   Code   RO         4488    i.Menu              menu.o
    0x08005744   0x08005744   0x00000064   Code   RO         4489    i.Menu_Init         menu.o
    0x080057a8   0x080057a8   0x00000004   Code   RO          472    i.NMI_Handler       stm32g4xx_it.o
    0x080057ac   0x080057ac   0x00000040   Code   RO         4682    i.OLED_Clear        oled.o
    0x080057ec   0x080057ec   0x0000001e   Code   RO         4683    i.OLED_ColorTurn    oled.o
    0x0800580a   0x0800580a   0x0000002e   Code   RO         4686    i.OLED_DisplayTurn  oled.o
    0x08005838   0x08005838   0x00000052   Code   RO         4688    i.OLED_Display_16x16  oled.o
    0x0800588a   0x0800588a   0x00000052   Code   RO         4690    i.OLED_Display_8x16  oled.o
    0x080058dc   0x080058dc   0x00000180   Code   RO         4691    i.OLED_Display_GB2312_string  oled.o
    0x08005a5c   0x08005a5c   0x000000de   Code   RO         4693    i.OLED_Init         oled.o
    0x08005b3a   0x08005b3a   0x00000002   PAD
    0x08005b3c   0x08005b3c   0x00000140   Code   RO         4694    i.OLED_ShowNum      oled.o
    0x08005c7c   0x08005c7c   0x0000008c   Code   RO         4695    i.OLED_WR_Byte      oled.o
    0x08005d08   0x08005d08   0x0000002e   Code   RO         4696    i.OLED_address      oled.o
    0x08005d36   0x08005d36   0x00000002   PAD
    0x08005d38   0x08005d38   0x00000058   Code   RO         4697    i.OLED_get_data_from_ROM  oled.o
    0x08005d90   0x08005d90   0x00000224   Code   RO         5034    i.PID_Control_Current_Process  pid_control.o
    0x08005fb4   0x08005fb4   0x0000000a   Code   RO         5035    i.PID_Control_Disable_All  pid_control.o
    0x08005fbe   0x08005fbe   0x00000002   PAD
    0x08005fc0   0x08005fc0   0x00000074   Code   RO         5036    i.PID_Control_Enable_Current  pid_control.o
    0x08006034   0x08006034   0x00000068   Code   RO         5037    i.PID_Control_Enable_Voltage  pid_control.o
    0x0800609c   0x0800609c   0x00000080   Code   RO         5040    i.PID_Control_Init  pid_control.o
    0x0800611c   0x0800611c   0x0000002c   Code   RO         5041    i.PID_Control_Loop  pid_control.o
    0x08006148   0x08006148   0x00000100   Code   RO         5043    i.PID_Control_Set_Mode  pid_control.o
    0x08006248   0x08006248   0x00000274   Code   RO         5046    i.PID_Control_Voltage_Process  pid_control.o
    0x080064bc   0x080064bc   0x0000011c   Code   RO         4927    i.PID_Current_Control  pid_yyds.o
    0x080065d8   0x080065d8   0x00000234   Code   RO         4928    i.PID_Debug_Info    pid_yyds.o
    0x0800680c   0x0800680c   0x00000044   Code   RO         4931    i.PID_Init          pid_yyds.o
    0x08006850   0x08006850   0x00000034   Code   RO         4933    i.PID_Reset         pid_yyds.o
    0x08006884   0x08006884   0x0000011c   Code   RO         4935    i.PID_SPWM_Depth_Control  pid_yyds.o
    0x080069a0   0x080069a0   0x00000002   Code   RO          473    i.PendSV_Handler    stm32g4xx_it.o
    0x080069a2   0x080069a2   0x00000002   PAD
    0x080069a4   0x080069a4   0x000000e8   Code   RO         4553    i.Process_AC_Data   my_adc.o
    0x08006a8c   0x08006a8c   0x0000012c   Code   RO         4554    i.Process_DC_Data   my_adc.o
    0x08006bb8   0x08006bb8   0x00000068   Code   RO         1382    i.RCC_GetSysClockFreqFromPLLSource  stm32g4xx_hal_rcc.o
    0x08006c20   0x08006c20   0x00000002   Code   RO          474    i.SVC_Handler       stm32g4xx_it.o
    0x08006c22   0x08006c22   0x00000002   PAD
    0x08006c24   0x08006c24   0x00000050   Code   RO         4698    i.Send_Command_to_ROM  oled.o
    0x08006c74   0x08006c74   0x00000008   Code   RO          475    i.SysTick_Handler   stm32g4xx_it.o
    0x08006c7c   0x08006c7c   0x00000068   Code   RO           14    i.SystemClock_Config  main.o
    0x08006ce4   0x08006ce4   0x00000014   Code   RO         4390    i.SystemInit        system_stm32g4xx.o
    0x08006cf8   0x08006cf8   0x00000018   Code   RO          476    i.TIM1_BRK_TIM15_IRQHandler  stm32g4xx_it.o
    0x08006d10   0x08006d10   0x00000010   Code   RO          477    i.TIM2_IRQHandler   stm32g4xx_it.o
    0x08006d20   0x08006d20   0x00000010   Code   RO          478    i.TIM6_DAC_IRQHandler  stm32g4xx_it.o
    0x08006d30   0x08006d30   0x00000010   Code   RO          479    i.TIM7_IRQHandler   stm32g4xx_it.o
    0x08006d40   0x08006d40   0x000000cc   Code   RO         2780    i.TIM_Base_SetConfig  stm32g4xx_hal_tim.o
    0x08006e0c   0x08006e0c   0x00000022   Code   RO         2781    i.TIM_CCxChannelCmd  stm32g4xx_hal_tim.o
    0x08006e2e   0x08006e2e   0x00000022   Code   RO         3478    i.TIM_CCxNChannelCmd  stm32g4xx_hal_tim_ex.o
    0x08006e50   0x08006e50   0x00000016   Code   RO         2791    i.TIM_ETR_SetConfig  stm32g4xx_hal_tim.o
    0x08006e66   0x08006e66   0x00000002   PAD
    0x08006e68   0x08006e68   0x00000018   Code   RO         2792    i.TIM_ITRx_SetConfig  stm32g4xx_hal_tim.o
    0x08006e80   0x08006e80   0x000000ac   Code   RO         2793    i.TIM_OC1_SetConfig  stm32g4xx_hal_tim.o
    0x08006f2c   0x08006f2c   0x000000b4   Code   RO         2794    i.TIM_OC2_SetConfig  stm32g4xx_hal_tim.o
    0x08006fe0   0x08006fe0   0x000000b4   Code   RO         2795    i.TIM_OC3_SetConfig  stm32g4xx_hal_tim.o
    0x08007094   0x08007094   0x000000b4   Code   RO         2796    i.TIM_OC4_SetConfig  stm32g4xx_hal_tim.o
    0x08007148   0x08007148   0x00000070   Code   RO         2797    i.TIM_OC5_SetConfig  stm32g4xx_hal_tim.o
    0x080071b8   0x080071b8   0x00000070   Code   RO         2798    i.TIM_OC6_SetConfig  stm32g4xx_hal_tim.o
    0x08007228   0x08007228   0x00000026   Code   RO         2800    i.TIM_TI1_ConfigInputStage  stm32g4xx_hal_tim.o
    0x0800724e   0x0800724e   0x00000002   PAD
    0x08007250   0x08007250   0x0000007c   Code   RO         2801    i.TIM_TI1_SetConfig  stm32g4xx_hal_tim.o
    0x080072cc   0x080072cc   0x00000028   Code   RO         2802    i.TIM_TI2_ConfigInputStage  stm32g4xx_hal_tim.o
    0x080072f4   0x080072f4   0x0000003a   Code   RO         2803    i.TIM_TI2_SetConfig  stm32g4xx_hal_tim.o
    0x0800732e   0x0800732e   0x00000038   Code   RO         2804    i.TIM_TI3_SetConfig  stm32g4xx_hal_tim.o
    0x08007366   0x08007366   0x0000003c   Code   RO         2805    i.TIM_TI4_SetConfig  stm32g4xx_hal_tim.o
    0x080073a2   0x080073a2   0x00000002   PAD
    0x080073a4   0x080073a4   0x00000058   Code   RO         4280    i.UARTEx_SetNbDataToProcess  stm32g4xx_hal_uart_ex.o
    0x080073fc   0x080073fc   0x000000f8   Code   RO         3884    i.UART_AdvFeatureConfig  stm32g4xx_hal_uart.o
    0x080074f4   0x080074f4   0x000000ea   Code   RO         3885    i.UART_CheckIdleState  stm32g4xx_hal_uart.o
    0x080075de   0x080075de   0x00000014   Code   RO         3886    i.UART_DMAAbortOnError  stm32g4xx_hal_uart.o
    0x080075f2   0x080075f2   0x00000002   PAD
    0x080075f4   0x080075f4   0x0000006c   Code   RO         3896    i.UART_EndRxTransfer  stm32g4xx_hal_uart.o
    0x08007660   0x08007660   0x00000030   Code   RO         3897    i.UART_EndTransmit_IT  stm32g4xx_hal_uart.o
    0x08007690   0x08007690   0x00000104   Code   RO         3899    i.UART_RxISR_16BIT  stm32g4xx_hal_uart.o
    0x08007794   0x08007794   0x00000200   Code   RO         3900    i.UART_RxISR_16BIT_FIFOEN  stm32g4xx_hal_uart.o
    0x08007994   0x08007994   0x00000104   Code   RO         3901    i.UART_RxISR_8BIT   stm32g4xx_hal_uart.o
    0x08007a98   0x08007a98   0x000001fc   Code   RO         3902    i.UART_RxISR_8BIT_FIFOEN  stm32g4xx_hal_uart.o
    0x08007c94   0x08007c94   0x00000384   Code   RO         3903    i.UART_SetConfig    stm32g4xx_hal_uart.o
    0x08008018   0x08008018   0x0000015c   Code   RO         3905    i.UART_Start_Receive_IT  stm32g4xx_hal_uart.o
    0x08008174   0x08008174   0x000000ac   Code   RO         3910    i.UART_WaitOnFlagUntilTimeout  stm32g4xx_hal_uart.o
    0x08008220   0x08008220   0x00000010   Code   RO          480    i.USART1_IRQHandler  stm32g4xx_it.o
    0x08008230   0x08008230   0x00000004   Code   RO          481    i.UsageFault_Handler  stm32g4xx_it.o
    0x08008234   0x08008234   0x00000020   Code   RO         5431    i.__0printf         mc_w.l(printfa.o)
    0x08008254   0x08008254   0x00000010   Code   RO         2553    i.__NVIC_GetPriorityGrouping  stm32g4xx_hal_cortex.o
    0x08008264   0x08008264   0x00000028   Code   RO         2554    i.__NVIC_SetPriority  stm32g4xx_hal_cortex.o
    0x0800828c   0x0800828c   0x0000000e   Code   RO         5507    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800829a   0x0800829a   0x00000002   Code   RO         5508    i.__scatterload_null  mc_w.l(handlers.o)
    0x0800829c   0x0800829c   0x0000000e   Code   RO         5509    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080082aa   0x080082aa   0x00000002   PAD
    0x080082ac   0x080082ac   0x00000184   Code   RO         5438    i._fp_digits        mc_w.l(printfa.o)
    0x08008430   0x08008430   0x000006b4   Code   RO         5439    i._printf_core      mc_w.l(printfa.o)
    0x08008ae4   0x08008ae4   0x00000024   Code   RO         5440    i._printf_post_padding  mc_w.l(printfa.o)
    0x08008b08   0x08008b08   0x0000002e   Code   RO         5441    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08008b36   0x08008b36   0x00000010   Code   RO         4699    i.delay             oled.o
    0x08008b46   0x08008b46   0x00000002   PAD
    0x08008b48   0x08008b48   0x0000007c   Code   RO         4490    i.display           menu.o
    0x08008bc4   0x08008bc4   0x00000058   Code   RO         4831    i.display_basic_info  task.o
    0x08008c1c   0x08008c1c   0x00000058   Code   RO         4832    i.display_dc_basic_info  task.o
    0x08008c74   0x08008c74   0x00000078   Code   RO         4833    i.display_dc_pid_status  task.o
    0x08008cec   0x08008cec   0x0000001c   Code   RO          412    i.fputc             usart.o
    0x08008d08   0x08008d08   0x00000040   Code   RO         4834    i.handle_menu_return  task.o
    0x08008d48   0x08008d48   0x000000c4   Code   RO         4427    i.key_scan          key.o
    0x08008e0c   0x08008e0c   0x000001a8   Code   RO           15    i.main              main.o
    0x08008fb4   0x08008fb4   0x00000034   Code   RO         4560    i.sliding_average_filter  my_adc.o
    0x08008fe8   0x08008fe8   0x00000018   Code   RO         4561    i.sliding_average_filter_current  my_adc.o
    0x08009000   0x08009000   0x00000018   Code   RO         4562    i.sliding_average_filter_dc_current  my_adc.o
    0x08009018   0x08009018   0x00000018   Code   RO         4563    i.sliding_average_filter_dc_voltage  my_adc.o
    0x08009030   0x08009030   0x00000018   Code   RO         4564    i.sliding_average_filter_voltage  my_adc.o
    0x08009048   0x08009048   0x00000060   Code   RO         4835    i.task1             task.o
    0x080090a8   0x080090a8   0x0000007c   Code   RO         4836    i.task2             task.o
    0x08009124   0x08009124   0x000000d8   Code   RO         4837    i.task3             task.o
    0x080091fc   0x080091fc   0x00000038   Code   RO         4838    i.task_init         task.o
    0x08009234   0x08009234   0x00000018   Data   RO         3911    .constdata          stm32g4xx_hal_uart.o
    0x0800924c   0x0800924c   0x00000010   Data   RO         4282    .constdata          stm32g4xx_hal_uart_ex.o
    0x0800925c   0x0800925c   0x00000018   Data   RO         4391    .constdata          system_stm32g4xx.o
    0x08009274   0x08009274   0x00000010   Data   RO         4566    .constdata          my_adc.o
    0x08009284   0x08009284   0x00000960   Data   RO         4839    .constdata          task.o
    0x08009be4   0x08009be4   0x00000056   Data   RO         4492    .conststring        menu.o
    0x08009c3a   0x08009c3a   0x00000002   PAD
    0x08009c3c   0x08009c3c   0x00000020   Data   RO         5505    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08009c5c, Size: 0x00000b50, Max: 0x00008000, ABSOLUTE, COMPRESSED[0x00000060])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000001   Data   RW          414    .data               usart.o
    0x20000001   COMPRESSED   0x00000003   PAD
    0x20000004   COMPRESSED   0x0000000c   Data   RW         1153    .data               stm32g4xx_hal.o
    0x20000010   COMPRESSED   0x00000004   Data   RW         4392    .data               system_stm32g4xx.o
    0x20000014   COMPRESSED   0x00000002   Data   RW         4428    .data               key.o
    0x20000016   COMPRESSED   0x00000002   PAD
    0x20000018   COMPRESSED   0x00000100   Data   RW         4493    .data               menu.o
    0x20000118   COMPRESSED   0x0000003c   Data   RW         4567    .data               my_adc.o
    0x20000154   COMPRESSED   0x00000004   Data   RW         4700    .data               oled.o
    0x20000158   COMPRESSED   0x00000024   Data   RW         4840    .data               task.o
    0x2000017c   COMPRESSED   0x0000000c   Data   RW         5048    .data               pid_control.o
    0x20000188   COMPRESSED   0x00000004   Data   RW         5481    .data               mc_w.l(stdout.o)
    0x2000018c        -       0x000000cc   Zero   RW          249    .bss                adc.o
    0x20000258        -       0x00000214   Zero   RW          324    .bss                tim.o
    0x2000046c        -       0x000000f8   Zero   RW          413    .bss                usart.o
    0x20000564        -       0x000000f0   Zero   RW         4565    .bss                my_adc.o
    0x20000654        -       0x000000c8   Zero   RW         4936    .bss                pid_yyds.o
    0x2000071c        -       0x00000034   Zero   RW         5047    .bss                pid_control.o
    0x20000750        -       0x00000400   Zero   RW            1    STACK               startup_stm32g431xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       448         36          0          0        204       1979   adc.o
        80          6          0          0          0        782   dma.o
       316         16          0          0          0       1051   gpio.o
       304         16          0          2          0       1249   key.o
       534        168          0          0          0     675669   main.o
       834         82         86        256          0       3789   menu.o
       916        168         16         60        240       8092   my_adc.o
      1672         94          0          4          0       9466   oled.o
      2630        862          0         12         52      10783   pid_control.o
      1252        270          0          0        200       5379   pid_yyds.o
        36          8        472          0       1024        824   startup_stm32g431xx.o
       194         32          0         12          0       4273   stm32g4xx_hal.o
      3416        136          0          0          0      84517   stm32g4xx_hal_adc.o
       288          8          0          0          0      68215   stm32g4xx_hal_adc_ex.o
       310         22          0          0          0      34299   stm32g4xx_hal_cortex.o
       832         22          0          0          0       4599   stm32g4xx_hal_dma.o
       486         32          0          0          0       2703   stm32g4xx_hal_gpio.o
        68          6          0          0          0        842   stm32g4xx_hal_msp.o
       300         18          0          0          0       1397   stm32g4xx_hal_pwr_ex.o
      2260        106          0          0          0       7188   stm32g4xx_hal_rcc.o
       880          8          0          0          0       1872   stm32g4xx_hal_rcc_ex.o
      5316        372          0          0          0      28554   stm32g4xx_hal_tim.o
       778         58          0          0          0       8604   stm32g4xx_hal_tim_ex.o
      5008        136         24          0          0      38280   stm32g4xx_hal_uart.o
       360         10         16          0          0       5386   stm32g4xx_hal_uart_ex.o
       138         38          0          0          0       7131   stm32g4xx_it.o
        20          6         24          4          0       1111   system_stm32g4xx.o
      1184        310       2400         36          0       6217   task.o
      1780        108          0          0        532       7642   tim.o
       300         26          0          1        248       2431   usart.o

    ----------------------------------------------------------------------
     32982       <USER>       <GROUP>        392       2500    1034324   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        42          0          2          5          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        86          0          0          0          0          0   __dczerorl2.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0        108   memseta.o
      2218         90          0          0          0        464   printfa.o
         0          0          0          4          0          0   stdout.o
        28          0          0          0          0         76   strcmp.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
      3974        <USER>          <GROUP>          4          0       2072   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2698        106          0          4          0       1092   mc_w.l
      1270          0          0          0          0        980   mf_w.l

    ----------------------------------------------------------------------
      3974        <USER>          <GROUP>          4          0       2072   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     36956       3286       3072        396       2500    1013892   Grand Totals
     36956       3286       3072         96       2500    1013892   ELF Image Totals (compressed)
     36956       3286       3072         96          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                40028 (  39.09kB)
    Total RW  Size (RW Data + ZI Data)              2896 (   2.83kB)
    Total ROM Size (Code + RO Data + RW Data)      40124 (  39.18kB)

==============================================================================

