[info] Log at : 2025/5/9|20:31:24|GMT+0800

[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[info] Log at : 2025/5/9|21:16:49|GMT+0800

[Warn] 发现无效的文件组，Group: ::CMSIS
[info] Log at : 2025/5/9|21:26:11|GMT+0800

[Warn] 发现无效的文件组，Group: ::CMSIS
[info] Log at : 2025/5/9|21:27:58|GMT+0800

[info] Log at : 2025/5/9|21:29:59|GMT+0800

[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[info] Log at : 2025/5/10|21:25:24|GMT+0800

[Warn] 发现无效的文件组，Group: ::CMSIS
[info] Log at : 2025/5/10|21:37:30|GMT+0800

[Warn] 发现无效的文件组，Group: ::CMSIS
[info] Log at : 2025/5/11|13:52:17|GMT+0800

[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[info] Log at : 2025/5/12|16:33:52|GMT+0800

[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[Warn] 发现无效的文件组，Group: ::CMSIS
[info] Log at : 2025/5/12|19:16:14|GMT+0800

[Warn] 发现无效的文件组，Group: ::CMSIS
[info] Log at : 2025/5/12|23:16:07|GMT+0800

[Warn] 发现无效的文件组，Group: ::CMSIS
[info] Log at : 2025/5/12|23:22:30|GMT+0800

Log at : 2025/5/12|23:27:07|GMT+0800

Log at : 2025/5/12|23:35:49|GMT+0800

Log at : 2025/5/12|23:37:44|GMT+0800

Log at : 2025/5/12|23:40:16|GMT+0800

Log at : 2025/5/13|08:12:17|GMT+0800

Log at : 2025/5/13|09:18:31|GMT+0800

Log at : 2025/5/16|18:11:47|GMT+0800

Log at : 2025/5/17|10:43:34|GMT+0800

Log at : 2025/5/18|12:14:09|GMT+0800

Log at : 2025/5/18|20:44:17|GMT+0800

Log at : 2025/5/19|08:29:27|GMT+0800

Log at : 2025/5/19|10:33:00|GMT+0800

Log at : 2025/5/19|11:45:08|GMT+0800

Log at : 2025/5/20|11:40:58|GMT+0800

Log at : 2025/5/21|12:21:00|GMT+0800

Log at : 2025/5/21|17:51:38|GMT+0800

Log at : 2025/5/21|18:23:02|GMT+0800

TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
Log at : 2025/5/23|11:53:55|GMT+0800

TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
TypeError: Cannot read properties of undefined (reading 'replace')
    at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
    at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
    at Generator.next (<anonymous>)
    at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
    at new Promise (<anonymous>)
    at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
    at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
    at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
    at Generator.next (<anonymous>)
    at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
Log at : 2025/5/23|12:40:30|GMT+0800

Log at : 2025/5/24|21:20:52|GMT+0800

Log at : 2025/5/24|21:21:22|GMT+0800

Log at : 2025/5/24|21:21:49|GMT+0800

Log at : 2025/5/26|16:14:31|GMT+0800

Log at : 2025/6/16|12:12:43|GMT+0800

Log at : 2025/6/16|12:14:25|GMT+0800

Log at : 2025/6/16|12:25:48|GMT+0800

Log at : 2025/6/16|12:30:23|GMT+0800

Log at : 2025/6/26|18:16:08|GMT+0800

Log at : 2025/6/26|18:25:58|GMT+0800

Log at : 2025/6/26|20:03:33|GMT+0800

Log at : 2025/6/27|09:52:43|GMT+0800

Log at : 2025/6/27|11:28:32|GMT+0800

Log at : 2025/6/27|16:09:49|GMT+0800

Log at : 2025/6/27|17:44:46|GMT+0800

Log at : 2025/6/27|20:53:09|GMT+0800

Log at : 2025/6/28|08:23:07|GMT+0800

[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
[TypeError: Cannot read properties of undefined (reading 'replace')
	at x.toAbsolutePath (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8358)
	at I.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:11207)
	at Generator.next (<anonymous>)
	at c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4480
	at new Promise (<anonymous>)
	at r (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4225)
	at I.load (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:10320)
	at x.<anonymous> (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:8120)
	at Generator.next (<anonymous>)
	at a (c:\Users\<USER>\.vscode\extensions\cl.keil-assistant-1.6.2\dist\extension.js:1:4282)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)]
