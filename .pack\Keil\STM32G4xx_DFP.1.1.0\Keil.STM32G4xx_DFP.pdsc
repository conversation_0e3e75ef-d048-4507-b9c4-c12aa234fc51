<?xml version="1.0" encoding="UTF-8"?>

<package schemaVersion="1.6.0" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>Keil</vendor>
  <name>STM32G4xx_DFP</name>
  <description>STMicroelectronics STM32G4 Series Device Support, Drivers and Examples</description>
  <url>http://www.keil.com/pack</url>

  <releases>
    <release version="1.1.0" date="2019-05-28">
      Initial public release of STM32G4 Series Device Support:
      - Requires STM32CubeMX Version 5.2 or higher and STM32CubeG4 Firmware Package V1.0.0
      Added debug sequences:
      - configure ETM Trace.
      Added board support for STM32G474E-EVAL:
      - Added Blinky example.
    </release>
    <!--
    <release version="1.0.0">
    first release - internal
    </release>
    -->
  </releases>

  <keywords>
    <keyword>ST</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package STMicroelectronics</keyword>
    <keyword>STM32G4</keyword>
    <keyword>STM32G4xx</keyword>
  </keywords>

  <devices>
    <family Dfamily="STM32G4 Series" Dvendor="STMicroelectronics:13">
      <processor Dcore="Cortex-M4" DcoreVersion="r0p1" Dendian="Little-endian" Dfpu="1" Dmpu="1"/>
      <book name="Documentation/dui0553a_cortex_m4_dgug.pdf" title="Cortex-M4 Generic User Guide"/>
      <book name="Documentation/RM0440_STM32G4xx_rev0.7.pdf" title="STM32G4xx Reference Manual"/>
      <description>
The STM32G4xx devices have an Arm Cortex-M4 with FPU core. It has the following features:
- using an adaptive real-time accelerator (ART Accelerator) allowing 0-wait-state execution from Flash memory.
- a frequency up to 170 MHz with 213 DMIPS,
- including MPU and DSP instructions.
- using 22 Kbytes of SRAM, with HW parity check implemented on the first 16 Kbytes; and  a Routine booster: 10 Kbytes of SRAM on instruction and data bus, with HW parity check (CCM SRAM).
- communication interfaces: FDCAN, I2C, USART/UART, LPUART, SPI, SAI, USB 2.0, IRTIM, USB Type-C
      </description>

      <sequences>
        <!-- Override for Pre-Defined Sequences -->
        <sequence name="DebugDeviceUnlock">
          <block>
            Sequence("CheckID");
          </block>
        </sequence>

        <sequence name="DebugCoreStart">
          <block>
            // Replication of Standard Functionality
            Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
          </block>

          <block info="DbgMCU registers">
            Write32(0xE0042004, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
            Write32(0xE0042008, DbgMCU_APB1_Fz1);                                   // DBGMCU_APB1_FZ1: Configure APB1 Peripheral Freeze Behavior
            Write32(0xE004200C, DbgMCU_APB1_Fz2);                                   // DBGMCU_APB1_FZ2: Configure APB1 Peripheral Freeze Behavior
            Write32(0xE0042010, DbgMCU_APB2_Fz);                                    // DBGMCU_APB2_FZ: Configure APB2 Peripheral Freeze Behavior
          </block>
        </sequence>

        <sequence name="TraceStart">
          <block>
            __var traceSWO    = (__traceout &amp; 0x1) != 0;                        // SWO (asynchronous) Trace Selected?
            __var traceTPIU   = (__traceout &amp; 0x2) != 0;                        // TPIU (synchronous) Trace Selected?
          </block>

          <control if="traceSWO">
            <block>
              Sequence("EnableTraceSWO");                                           // Call SWO Trace Setup
            </block>
          </control>

          <control if="traceTPIU">
            <block>
              Sequence("EnableTraceTPIU");                                          // Call TPIU Trace Setup
            </block>
          </control>
        </sequence>

        <sequence name="TraceStop">
          <block>
            // Nothing required for SWO Trace
            __var traceSWO    = (__traceout &amp; 0x1) != 0;                        // SWO enabled?
            __var traceTPIU   = (__traceout &amp; 0x2) != 0;                        // Synchronous trace port enabled?
          </block>

          <control if="traceSWO">
            <block>
              Sequence("DisableTraceSWO");
            </block>
          </control>

          <control if="traceTPIU">
            <block>
              Sequence("DisableTraceTPIU");
            </block>
          </control>
        </sequence>

        <!-- User-Defined Sequences -->
        <sequence name="CheckID">
          <block>
            __var pidr1 = 0;
            __var pidr2 = 0;
            __var jep106id = 0;
            __var ROMTableBase = 0;

            __ap = 0;      // AHB-AP

            ROMTableBase = ReadAP(0xF8) &amp; ~0x3;

            pidr1 = Read32(ROMTableBase + 0x0FE4);
            pidr2 = Read32(ROMTableBase + 0x0FE8);
            jep106id = ((pidr2 &amp; 0x7) &lt;&lt; 4 ) | ((pidr1 &gt;&gt; 4) &amp; 0xF);
          </block>

          <control if="jep106id != 0x20">
            <block>
              Query(0, "Not a genuine ST Device! Abort connection", 1);
              Message(2, "Not a genuine ST Device! Abort connection.");
            </block>
          </control>
        </sequence>

        <sequence name="EnableTraceSWO">
          <block>
            __var dbgmcu_val       = 0;                                                   // DBGMCU_CR Value
            __var dbgmcu_trace_val = 0;                                                   // DBGMCU_CR Value

            dbgmcu_val        = Read32(0xE0042004) &amp; (~0xE0);                         // Read DBGMCU_CR and clear trace setup
            dbgmcu_trace_val  = (1 &lt;&lt; 5);                                           // Trace I/O Enable + Trace Mode Asynchronous

            Sequence("ConfigureTraceSWOPin");
          </block>

          <block info="configure Trace I/O Enable + Trace Mode Asynchronous">
            Write32(0xE0042004, dbgmcu_val | dbgmcu_trace_val);                           // Write DBGMCU_CR: Trace Settings
          </block>
        </sequence>

        <sequence name="DisableTraceSWO">
          <block>
            __var dbgmcu_val       = 0;                                                   // DBGMCU_CR Value
          </block>

          <block info="unconfigure Trace I/O Enable + Trace Mode Asynchronous">
            dbgmcu_val = Read32(0xE0042004) &amp; (~0xE0);                                // Read DBGMCU_CR and clear trace setup
            Write32(0xE0042004, dbgmcu_val);                                              // Write DBGMCU_CR: Trace Settings
          </block>
        </sequence>

        <sequence name="EnableTraceTPIU">
          <block>
            __var width            = (__traceout &amp; 0x003F0000) &gt;&gt; 16;
            __var dbgmcu_val       = 0;                                                   // DBGMCU_CR Value
            __var dbgmcu_trace_val = 0;                                                   // DBGMCU_CR Value

            dbgmcu_val        = Read32(0xE0042004) &amp; (~0xE0);                         // Read DBGMCU_CR and clear trace setup

            Sequence("ConfigureTraceTPIUPins");
          </block>

          <control if="width &gt;= 1" info="TPIU port width 1">
            <block info="configure Trace I/O Enable + Trace Mode Synchronous 1 bit">
              dbgmcu_trace_val  = (3 &lt;&lt; 5);
            </block>
          </control>

          <control if="width &gt;= 2" info="TPIU port width 2">
            <block info="configure Trace I/O Enable + Trace Mode Synchronous 2 bit">
              dbgmcu_trace_val  = (5 &lt;&lt; 5);
            </block>
          </control>

          <control if="width &gt;= 4" info="TPIU port width 4">
            <block info="configure Trace I/O Enable + Trace Mode Synchronous 4 bit">
              dbgmcu_trace_val  = (7 &lt;&lt; 5);
            </block>
          </control>

          <block info="configure Trace I/O Enable + Trace Mode Asynchronous">
            Write32(0xE0042004, dbgmcu_val | dbgmcu_trace_val);                           // Write DBGMCU_CR: Trace Settings
          </block>
        </sequence>

        <sequence name="DisableTraceTPIU">
          <block>
            __var dbgmcu_val       = 0;                                                   // DBGMCU_CR Value
          </block>

          <block info="unconfigure Trace I/O Enable + Trace Mode Synchronous">
            dbgmcu_val = Read32(0xE0042004) &amp; (~0xE0);                                // Read DBGMCU_CR and clear trace setup
            Write32(0xE0042004, dbgmcu_val);                                              // Write DBGMCU_CR: Trace Settings
          </block>
        </sequence>

        <sequence name="ConfigureTraceSWOPin">
          <block>
            __var pin     = 0;
            __var port    = 0;
            __var portAdr = 0;
            __var pos     = 0;

            __var SWO_Pin = 0x00010003;          // PB3
          </block>

            <!-- configure SWO -->
            <block info="configure SWO">
              pin     =               ((SWO_Pin            ) &amp; 0x0000FFFF);
              port    =               ((SWO_Pin &gt;&gt; 16) &amp; 0x0000FFFF);
              portAdr = 0x48000000 + (((SWO_Pin &gt;&gt; 16) &amp; 0x0000FFFF) * 0x400);

              pos = pin * 2;
              Write32(0x4002104C,     ((Read32(0x4002104C    )                         ) | (1 &lt;&lt; port)) );  // RCC_AHB2ENR:   IO port clock enable
              Write32(portAdr + 0x00, ((Read32(portAdr + 0x00) &amp; ~( 3 &lt;&lt; pos)) | (2 &lt;&lt; pos )) );  // GPIOx_MODER:   Set Mode (Alternate Function)
              Write32(portAdr + 0x08, ((Read32(portAdr + 0x08)                         ) | (3 &lt;&lt; pos )) );  // GPIOx_OSPEEDR: Set Speed (Very High Speed)
              Write32(portAdr + 0x0C, ((Read32(portAdr + 0x0C) &amp; ~( 3 &lt;&lt; pos))                    ) );  // GPIOx_PUPDR:   Set I/O to no pull-up/pull-down
            </block>
            <control if="pin &lt;  8">
              <block>
              pos = ((pin    ) &amp; 7) * 4;
              Write32(portAdr + 0x20, ((Read32(portAdr + 0x20) &amp; ~(15 &lt;&lt; pos))                    ) );  // GPIOx_AFRL:    Alternate Function to AF0
              </block>
            </control>
            <control if="pin &gt;= 8">
              <block>
              pos = ((pin - 8) &amp; 7) * 4;
              Write32(portAdr + 0x24, ((Read32(portAdr + 0x24) &amp; ~(15 &lt;&lt; pos))                    ) );  // GPIOx_AFRH:    Alternate Function to AF0
              </block>
            </control>
        </sequence>

        <sequence name="ConfigureTraceTPIUPins">
          <block>
            __var pin     = 8;
            __var port    = 0;
            __var portAdr = 0;
            __var pos     = 0;

            __var width   = (__traceout &amp; 0x003F0000) &gt;&gt; 16;
          </block>

            <!-- configure TRACECLK -->
            <block info="configure TRACECLK">
              pin     =                (TraceClk_Pin            ) &amp; 0x0000FFFF;
              port    =                (TraceClk_Pin &gt;&gt; 16) &amp; 0x0000FFFF;
              portAdr = 0x48000000 + (((TraceClk_Pin &gt;&gt; 16) &amp; 0x0000FFFF) * 0x400);

              pos = pin * 2;
              Write32(0x4002104C,     ((Read32(0x4002104C    )                         ) | (1 &lt;&lt; port)) );  // RCC_AHB2ENR:   IO port clock enable
              Write32(portAdr + 0x00, ((Read32(portAdr + 0x00) &amp; ~( 3 &lt;&lt; pos)) | (2 &lt;&lt; pos )) );  // GPIOx_MODER:   Set Mode (Alternate Function)
              Write32(portAdr + 0x08, ((Read32(portAdr + 0x08)                         ) | (3 &lt;&lt; pos )) );  // GPIOx_OSPEEDR: Set Speed (Very High Speed)
              Write32(portAdr + 0x0C, ((Read32(portAdr + 0x0C) &amp; ~( 3 &lt;&lt; pos))                    ) );  // GPIOx_PUPDR:   Set I/O to no pull-up/pull-down
            </block>
            <control if="pin &lt;  8">
              <block>
              pos = ((pin    ) &amp; 7) * 4;
              Write32(portAdr + 0x20, ((Read32(portAdr + 0x20) &amp; ~(15 &lt;&lt; pos))                    ) );  // GPIOx_AFRL:    Alternate Function to AF0
              </block>
            </control>
            <control if="pin &gt;= 8">
              <block>
              pos = ((pin - 8) &amp; 7) * 4;
              Write32(portAdr + 0x24, ((Read32(portAdr + 0x24) &amp; ~(15 &lt;&lt; pos))                    ) );  // GPIOx_AFRH:    Alternate Function to AF0
              </block>
            </control>

          <control if="width &gt;= 1" info="TPIU port width 1">
            <!-- configure TRACED0 -->
            <block info="configure TRACED0">
              pin     =                (TraceD0_Pin            ) &amp; 0x0000FFFF;
              port    =                (TraceD0_Pin &gt;&gt; 16) &amp; 0x0000FFFF;
              portAdr = 0x48000000 + (((TraceD0_Pin &gt;&gt; 16) &amp; 0x0000FFFF) * 0x400);

              pos = pin * 2;
              Write32(0x4002104C,     ((Read32(0x4002104C    )                         ) | (1 &lt;&lt; port)) );  // RCC_AHB2ENR:   IO port clock enable
              Write32(portAdr + 0x00, ((Read32(portAdr + 0x00) &amp; ~( 3 &lt;&lt; pos)) | (2 &lt;&lt; pos )) );  // GPIOx_MODER:   Set Mode (Alternate Function)
              Write32(portAdr + 0x08, ((Read32(portAdr + 0x08)                         ) | (3 &lt;&lt; pos )) );  // GPIOx_OSPEEDR: Set Speed (Very High Speed)
              Write32(portAdr + 0x0C, ((Read32(portAdr + 0x0C) &amp; ~( 3 &lt;&lt; pos))                    ) );  // GPIOx_PUPDR:   Set I/O to no pull-up/pull-down
            </block>
            <control if="pin &lt;  8">
              <block>
              pos = ((pin    ) &amp; 7) * 4;
              Write32(portAdr + 0x20, ((Read32(portAdr + 0x20) &amp; ~(15 &lt;&lt; pos))                    ) );  // GPIOx_AFRL:    Alternate Function to AF0
              </block>
            </control>
            <control if="pin &gt;= 8">
              <block>
              pos = ((pin - 8) &amp; 7) * 4;
              Write32(portAdr + 0x24, ((Read32(portAdr + 0x24) &amp; ~(15 &lt;&lt; pos))                    ) );  // GPIOx_AFRH:    Alternate Function to AF0
              </block>
            </control>
          </control>

          <control if="width &gt;= 2" info="TPIU port width 2">
            <!-- configure TRACED1 -->
            <block info="configure TRACED1">
              pin     =                (TraceD1_Pin            ) &amp; 0x0000FFFF;
              port    =                (TraceD1_Pin &gt;&gt; 16) &amp; 0x0000FFFF;
              portAdr = 0x48000000 + (((TraceD1_Pin &gt;&gt; 16) &amp; 0x0000FFFF) * 0x400);

              pos = pin * 2;
              Write32(0x4002104C,     ((Read32(0x4002104C    )                         ) | (1 &lt;&lt; port)) );  // RCC_AHB2ENR:   IO port clock enable
              Write32(portAdr + 0x00, ((Read32(portAdr + 0x00) &amp; ~( 3 &lt;&lt; pos)) | (2 &lt;&lt; pos )) );  // GPIOx_MODER:   Set Mode (Alternate Function)
              Write32(portAdr + 0x08, ((Read32(portAdr + 0x08)                         ) | (3 &lt;&lt; pos )) );  // GPIOx_OSPEEDR: Set Speed (Very High Speed)
              Write32(portAdr + 0x0C, ((Read32(portAdr + 0x0C) &amp; ~( 3 &lt;&lt; pos))                    ) );  // GPIOx_PUPDR:   Set I/O to no pull-up/pull-down
            </block>
            <control if="pin &lt;  8">
              <block>
              pos = ((pin    ) &amp; 7) * 4;
              Write32(portAdr + 0x20, ((Read32(portAdr + 0x20) &amp; ~(15 &lt;&lt; pos))                    ) );  // GPIOx_AFRL:    Alternate Function to AF0
              </block>
            </control>
            <control if="pin &gt;= 8">
              <block>
              pos = ((pin - 8) &amp; 7) * 4;
              Write32(portAdr + 0x24, ((Read32(portAdr + 0x24) &amp; ~(15 &lt;&lt; pos))                    ) );  // GPIOx_AFRH:    Alternate Function to AF0
              </block>
            </control>
          </control>

          <control if="width &gt;= 4" info="TPIU port width 4">
            <!-- configure TRACED2 -->
            <block info="configure TRACED2">
              pin     =                (TraceD2_Pin            ) &amp; 0x0000FFFF;
              port    =                (TraceD2_Pin &gt;&gt; 16) &amp; 0x0000FFFF;
              portAdr = 0x48000000 + (((TraceD2_Pin &gt;&gt; 16) &amp; 0x0000FFFF) * 0x400);

              pos = pin * 2;
              Write32(0x4002104C,     ((Read32(0x4002104C    )                         ) | (1 &lt;&lt; port)) );  // RCC_AHB2ENR:   IO port clock enable
              Write32(portAdr + 0x00, ((Read32(portAdr + 0x00) &amp; ~( 3 &lt;&lt; pos)) | (2 &lt;&lt; pos )) );  // GPIOx_MODER:   Set Mode (Alternate Function)
              Write32(portAdr + 0x08, ((Read32(portAdr + 0x08)                         ) | (3 &lt;&lt; pos )) );  // GPIOx_OSPEEDR: Set Speed (Very High Speed)
              Write32(portAdr + 0x0C, ((Read32(portAdr + 0x0C) &amp; ~( 3 &lt;&lt; pos))                    ) );  // GPIOx_PUPDR:   Set I/O to no pull-up/pull-down
            </block>
            <control if="pin &lt;  8">
              <block>
              pos = ((pin    ) &amp; 7) * 4;
              Write32(portAdr + 0x20, ((Read32(portAdr + 0x20) &amp; ~(15 &lt;&lt; pos))                    ) );  // GPIOx_AFRL:    Alternate Function to AF0
              </block>
            </control>
            <control if="pin &gt;= 8">
              <block>
              pos = ((pin - 8) &amp; 7) * 4;
              Write32(portAdr + 0x24, ((Read32(portAdr + 0x24) &amp; ~(15 &lt;&lt; pos))                    ) );  // GPIOx_AFRH:    Alternate Function to AF0
              </block>
            </control>

            <!-- configure TRACED3 -->
            <block info="configure TRACED3">
              pin     =                (TraceD3_Pin            ) &amp; 0x0000FFFF;
              port    =                (TraceD3_Pin &gt;&gt; 16) &amp; 0x0000FFFF;
              portAdr = 0x48000000 + (((TraceD3_Pin &gt;&gt; 16) &amp; 0x0000FFFF) * 0x400);

              pos = pin * 2;
              Write32(0x4002104C,     ((Read32(0x4002104C    )                         ) | (1 &lt;&lt; port)) );  // RCC_AHB2ENR:   IO port clock enable
              Write32(portAdr + 0x00, ((Read32(portAdr + 0x00) &amp; ~( 3 &lt;&lt; pos)) | (2 &lt;&lt; pos )) );  // GPIOx_MODER:   Set Mode (Alternate Function)
              Write32(portAdr + 0x08, ((Read32(portAdr + 0x08)                         ) | (3 &lt;&lt; pos )) );  // GPIOx_OSPEEDR: Set Speed (Very High Speed)
              Write32(portAdr + 0x0C, ((Read32(portAdr + 0x0C) &amp; ~( 3 &lt;&lt; pos))                    ) );  // GPIOx_PUPDR:   Set I/O to no pull-up/pull-down
            </block>
            <control if="pin &lt;  8">
              <block>
              pos = ((pin    ) &amp; 7) * 4;
              Write32(portAdr + 0x20, ((Read32(portAdr + 0x20) &amp; ~(15 &lt;&lt; pos))                    ) );  // GPIOx_AFRL:    Alternate Function to AF0
              </block>
            </control>
            <control if="pin &gt;= 8">
              <block>
              pos = ((pin - 8) &amp; 7) * 4;
              Write32(portAdr + 0x24, ((Read32(portAdr + 0x24) &amp; ~(15 &lt;&lt; pos))                    ) );  // GPIOx_AFRH:    Alternate Function to AF0
              </block>
            </control>
          </control>
        </sequence>
      </sequences>

      <!-- ************************  Subfamily 'STM32G431'  **************************** -->
      <subFamily DsubFamily="STM32G431">
        <processor Dclock="170000000" />
        <compile header="Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h" define="STM32G431xx"/>
        <debugvars configfile="CMSIS/Debug/STM32G4xx.dbgconf">
          __var DbgMCU_CR       = 0x00000007;   // DBGMCU_CR: DBG_SLEEP, DBG_STOP, DBG_STANDBY
          __var DbgMCU_APB1_Fz1 = 0x00000000;   // DGBMCU_APB1_FZ1: All Peripherals Operate as in Normal Mode
          __var DbgMCU_APB1_Fz2 = 0x00000000;   // DGBMCU_APB1_FZ2: All Peripherals Operate as in Normal Mode
          __var DbgMCU_APB2_Fz  = 0x00000000;   // DGBMCU_APB2_FZ: All Peripherals Operate as in Normal Mode
          __var TraceClk_Pin    = 0x00040002;   // PE2
          __var TraceD0_Pin     = 0x00040003;   // PE3
          __var TraceD1_Pin     = 0x00040004;   // PE4
          __var TraceD2_Pin     = 0x00040005;   // PE5
          __var TraceD3_Pin     = 0x00040006;   // PE6
        </debugvars>
        <debug svd="CMSIS/SVD/STM32G431xx.svd"/>

        <book name="Documentation/DS12589.pdf" title="STM32G431xx Data Sheet"/>

        <memory id="IRAM1" start="0x20000000" size="0x00008000" init="0" default="1" />

        <!-- ################################### 128 KB ################################### -->
        <!-- *************************  Device 'STM32G431CB'  ***************************** -->
        <device Dname="STM32G431CBTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>
        <device Dname="STM32G431CBUx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>
        <device Dname="STM32G431CBYx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="CSP" n="49"/>
        </device>

        <!-- *************************  Device 'STM32G431KB'  ***************************** -->
        <device Dname="STM32G431KBTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="32"/>
        </device>
        <device Dname="STM32G431KBUx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="32"/>
        </device>

        <!-- *************************  Device 'STM32G431RB'  ***************************** -->
        <device Dname="STM32G431RBTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="64"/>
        </device>
        <device Dname="STM32G431RBIx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="BGA" n="64"/>
        </device>

        <!-- *************************  Device 'STM32G431VB'  ***************************** -->
        <device Dname="STM32G431VBTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="100"/>
        </device>

        <!-- ################################### 64 KB ################################### -->
        <!-- *************************  Device 'STM32G431C8'  ***************************** -->
        <device Dname="STM32G431C8Tx">
          <memory id="IROM1"                              start="0x08000000" size="0x00010000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_64.FLM"  start="0x08000000" size="0x00010000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>
        <device Dname="STM32G431C8Ux">
          <memory id="IROM1"                              start="0x08000000" size="0x00010000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_64.FLM"  start="0x08000000" size="0x00010000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>

        <!-- *************************  Device 'STM32G431K8'  ***************************** -->
        <device Dname="STM32G431K8Tx">
          <memory id="IROM1"                              start="0x08000000" size="0x00010000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_64.FLM"  start="0x08000000" size="0x00010000"             default="1" />
          <feature type="QFP" n="32"/>
        </device>
        <device Dname="STM32G431K8Ux">
          <memory id="IROM1"                              start="0x08000000" size="0x00010000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_64.FLM"  start="0x08000000" size="0x00010000"             default="1" />
          <feature type="QFP" n="32"/>
        </device>
        <!-- *************************  Device 'STM32G431M8'  ***************************** -->
        <device Dname="STM32G431M8Tx">
          <memory id="IROM1"                              start="0x08000000" size="0x00010000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_64.FLM"  start="0x08000000" size="0x00010000"             default="1" />
          <feature type="QFP" n="80"/>
        </device>

        <!-- *************************  Device 'STM32G431R8'  ***************************** -->
        <device Dname="STM32G431R8Tx">
          <memory id="IROM1"                              start="0x08000000" size="0x00010000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_64.FLM"  start="0x08000000" size="0x00010000"             default="1" />
          <feature type="QFP" n="64"/>
        </device>
        <device Dname="STM32G431R8Ix">
          <memory id="IROM1"                              start="0x08000000" size="0x00010000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_64.FLM"  start="0x08000000" size="0x00010000"             default="1" />
          <feature type="BGA" n="64"/>
        </device>

        <!-- *************************  Device 'STM32G431V8'  ***************************** -->
        <device Dname="STM32G431V8Tx">
          <memory id="IROM1"                              start="0x08000000" size="0x00010000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_64.FLM"  start="0x08000000" size="0x00010000"             default="1" />
          <feature type="QFP" n="100"/>
        </device>

        <!-- ################################### 32 KB ################################### -->
        <!-- *************************  Device 'STM32G431C6'  ***************************** -->
        <device Dname="STM32G431C6Tx">
          <memory id="IROM1"                              start="0x08000000" size="0x00008000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_32.FLM"  start="0x08000000" size="0x00008000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>
        <device Dname="STM32G431C6Ux">
          <memory id="IROM1"                              start="0x08000000" size="0x00008000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_32.FLM"  start="0x08000000" size="0x00008000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>

        <!-- *************************  Device 'STM32G431K6'  ***************************** -->
        <device Dname="STM32G431K6Tx">
          <memory id="IROM1"                              start="0x08000000" size="0x00008000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_32.FLM"  start="0x08000000" size="0x00008000"             default="1" />
          <feature type="QFP" n="32"/>
        </device>
        <device Dname="STM32G431K6Ux">
          <memory id="IROM1"                              start="0x08000000" size="0x00008000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_32.FLM"  start="0x08000000" size="0x00008000"             default="1" />
          <feature type="QFP" n="32"/>
        </device>

        <!-- *************************  Device 'STM32G431M6'  ***************************** -->
        <device Dname="STM32G431M6Tx">
          <memory id="IROM1"                              start="0x08000000" size="0x00008000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_32.FLM"  start="0x08000000" size="0x00008000"             default="1" />
          <feature type="QFP" n="80"/>
        </device>

        <!-- *************************  Device 'STM32G431R6'  ***************************** -->
        <device Dname="STM32G431R6Tx">
          <memory id="IROM1"                              start="0x08000000" size="0x00008000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_32.FLM"  start="0x08000000" size="0x00008000"             default="1" />
          <feature type="QFP" n="64"/>
        </device>
        <device Dname="STM32G431R6Ix">
          <memory id="IROM1"                              start="0x08000000" size="0x00008000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_32.FLM"  start="0x08000000" size="0x00008000"             default="1" />
          <feature type="BGA" n="64"/>
        </device>

        <!-- *************************  Device 'STM32G431V6'  ***************************** -->
        <device Dname="STM32G431V6Tx">
          <memory id="IROM1"                              start="0x08000000" size="0x00008000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_32.FLM"  start="0x08000000" size="0x00008000"             default="1" />
          <feature type="QFP" n="100"/>
        </device>
      </subFamily>

      <!-- ************************  Subfamily 'STM32G441'  **************************** -->
      <subFamily DsubFamily="STM32G441">
        <processor Dclock="150000000" />
        <compile header="Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h" define="STM32G441xx"/>
        <debugvars configfile="CMSIS/Debug/STM32G4xx.dbgconf">
          __var DbgMCU_CR       = 0x00000007;   // DBGMCU_CR: DBG_SLEEP, DBG_STOP, DBG_STANDBY
          __var DbgMCU_APB1_Fz1 = 0x00000000;   // DGBMCU_APB1_FZ1: All Peripherals Operate as in Normal Mode
          __var DbgMCU_APB1_Fz2 = 0x00000000;   // DGBMCU_APB1_FZ2: All Peripherals Operate as in Normal Mode
          __var DbgMCU_APB2_Fz  = 0x00000000;   // DGBMCU_APB2_FZ: All Peripherals Operate as in Normal Mode
          __var TraceClk_Pin    = 0x00040002;   // PE2
          __var TraceD0_Pin     = 0x00040003;   // PE3
          __var TraceD1_Pin     = 0x00040004;   // PE4
          __var TraceD2_Pin     = 0x00040005;   // PE5
          __var TraceD3_Pin     = 0x00040006;   // PE6
        </debugvars>
        <debug svd="CMSIS/SVD/STM32G441xx.svd"/>

        <book name="Documentation/DocID031375.pdf" title="STM32G441xx Data Brief"/>

        <memory id="IRAM1" start="0x20000000" size="0x00008000" init="0" default="1" />

        <!-- ################################### 128 KB ################################### -->
        <!-- *************************  Device 'STM32G441CB'  ***************************** -->
        <device Dname="STM32G441CBTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>
        <device Dname="STM32G441CBUx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>
        <device Dname="STM32G441CBYx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="CSP" n="49"/>
        </device>

        <!-- *************************  Device 'STM32G441KB'  ***************************** -->
        <device Dname="STM32G441KBTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="32"/>
        </device>
        <device Dname="STM32G441KBUx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="32"/>
        </device>

        <!-- *************************  Device 'STM32G441RB'  ***************************** -->
        <device Dname="STM32G441RBTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="64"/>
        </device>
        <device Dname="STM32G441RBIx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="BGA" n="64"/>
        </device>

        <!-- *************************  Device 'STM32G441VB'  ***************************** -->
        <device Dname="STM32G441VBTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="64"/>
        </device>
      </subFamily>

      <!-- ************************  Subfamily 'STM32G471'  **************************** -->
      <subFamily DsubFamily="STM32G471">
        <processor Dclock="170000000" />
        <compile header="Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h" define="STM32G471xx"/>
        <debugvars configfile="CMSIS/Debug/STM32G4xx.dbgconf">
          __var DbgMCU_CR       = 0x00000007;   // DBGMCU_CR: DBG_SLEEP, DBG_STOP, DBG_STANDBY
          __var DbgMCU_APB1_Fz1 = 0x00000000;   // DGBMCU_APB1_FZ1: All Peripherals Operate as in Normal Mode
          __var DbgMCU_APB1_Fz2 = 0x00000000;   // DGBMCU_APB1_FZ2: All Peripherals Operate as in Normal Mode
          __var DbgMCU_APB2_Fz  = 0x00000000;   // DGBMCU_APB2_FZ: All Peripherals Operate as in Normal Mode
          __var TraceClk_Pin    = 0x00040002;   // PE2
          __var TraceD0_Pin     = 0x00040003;   // PE3
          __var TraceD1_Pin     = 0x00040004;   // PE4
          __var TraceD2_Pin     = 0x00040005;   // PE5
          __var TraceD3_Pin     = 0x00040006;   // PE6
        </debugvars>
        <debug svd="CMSIS/SVD/STM32G471xx.svd"/>

        <book name="Documentation/DS12728.pdf" title="STM32G471xx Data Sheet"/>

        <memory id="IRAM1" start="0x20000000" size="0x00020000" init="0" default="1" />

        <!-- ################################### 512 KB ################################## -->
        <!-- *************************  Device 'STM32G471CE'  ***************************** -->
        <device Dname="STM32G471CETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="48"/>
        </device>
        <device Dname="STM32G471CEUx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="48"/>
        </device>

        <!-- *************************  Device 'STM32G471ME'  ***************************** -->
        <device Dname="STM32G471METx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="80"/>
        </device>
        <device Dname="STM32G471MEYx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="CSP" n="81"/>
        </device>

        <!-- *************************  Device 'STM32G471RE'  ***************************** -->
        <device Dname="STM32G471RE">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="64"/>
        </device>

        <!-- *************************  Device 'STM32G471VE'  ***************************** -->
        <device Dname="STM32G471VETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="100"/>
        </device>
        <device Dname="STM32G471VEHx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="BGA" n="100"/>
        </device>
        <device Dname="STM32G471VEIx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="BGA" n="100"/>
        </device>

        <!-- *************************  Device 'STM32G471QE'  ***************************** -->
        <device Dname="STM32G471QETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="128"/>
        </device>

        <!-- ################################### 256 KB ################################## -->
        <!-- *************************  Device 'STM32G471CC'  ***************************** -->
        <device Dname="STM32G471CCTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>
        <device Dname="STM32G471CCUx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>

        <!-- *************************  Device 'STM32G471MC'  ***************************** -->
        <device Dname="STM32G471MCTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="QFP" n="80"/>
        </device>

        <!-- *************************  Device 'STM32G471QC'  ***************************** -->
        <device Dname="STM32G471QCTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="QFP" n="128"/>
        </device>

        <!-- *************************  Device 'STM32G471RC'  ***************************** -->
        <device Dname="STM32G471RCTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="QFP" n="64"/>
        </device>

        <!-- *************************  Device 'STM32G471VC'  ***************************** -->
        <device Dname="STM32G471VCTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="QFP" n="100"/>
        </device>
        <device Dname="STM32G471VCHx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="BGA" n="100"/>
        </device>
        <device Dname="STM32G471VCIx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="BGA" n="100"/>
        </device>
      </subFamily>

      <!-- ************************  Subfamily 'STM32G473'  **************************** -->
      <subFamily DsubFamily="STM32G473">
        <processor Dclock="150000000" />
        <compile header="Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h" define="STM32G473xx"/>
        <debugvars configfile="CMSIS/Debug/STM32G4xx.dbgconf">
          __var DbgMCU_CR       = 0x00000007;   // DBGMCU_CR: DBG_SLEEP, DBG_STOP, DBG_STANDBY
          __var DbgMCU_APB1_Fz1 = 0x00000000;   // DGBMCU_APB1_FZ1: All Peripherals Operate as in Normal Mode
          __var DbgMCU_APB1_Fz2 = 0x00000000;   // DGBMCU_APB1_FZ2: All Peripherals Operate as in Normal Mode
          __var DbgMCU_APB2_Fz  = 0x00000000;   // DGBMCU_APB2_FZ: All Peripherals Operate as in Normal Mode
          __var TraceClk_Pin    = 0x00040002;   // PE2
          __var TraceD0_Pin     = 0x00040003;   // PE3
          __var TraceD1_Pin     = 0x00040004;   // PE4
          __var TraceD2_Pin     = 0x00040005;   // PE5
          __var TraceD3_Pin     = 0x00040006;   // PE6
        </debugvars>
        <debug svd="CMSIS/SVD/STM32G473xx.svd"/>

        <book name="Documentation/DS12712.pdf" title="STM32G473xx Data Sheet"/>

        <memory id="IRAM1" start="0x20000000" size="0x00020000" init="0" default="1" />

        <!-- #################################### 512 KB ################################# -->
        <!-- *************************  Device 'STM32G473CE'  ***************************** -->
        <device Dname="STM32G473CETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="48"/>
        </device>
        <device Dname="STM32G473CEUx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="48"/>
        </device>

        <!-- *************************  Device 'STM32G473ME'  ***************************** -->
        <device Dname="STM32G473METx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="80"/>
        </device>
        <device Dname="STM32G473MEUx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="CSP" n="81"/>
        </device>

        <!-- *************************  Device 'STM32G473RE'  ***************************** -->
        <device Dname="STM32G473RETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="64"/>
        </device>

        <!-- *************************  Device 'STM32G473VE'  ***************************** -->
        <device Dname="STM32G473VETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="100"/>
        </device>
        <device Dname="STM32G473VEHx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="BGA" n="100"/>
        </device>
        <device Dname="STM32G473VEIx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="BGA" n="100"/>
        </device>

        <!-- *************************  Device 'STM32G473QE'  ***************************** -->
        <device Dname="STM32G473QETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="128"/>
        </device>

        <!-- #################################### 256 KB ################################# -->
        <!-- *************************  Device 'STM32G473CC'  ***************************** -->
        <device Dname="STM32G473CCTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>
        <device Dname="STM32G473CCUx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>

        <!-- *************************  Device 'STM32G473MC'  ***************************** -->
        <device Dname="STM32G473MCTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="QFP" n="80"/>
        </device>

        <!-- *************************  Device 'STM32G473QC'  ***************************** -->
        <device Dname="STM32G473QCTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="QFP" n="128"/>
        </device>

        <!-- *************************  Device 'STM32G473QC'  ***************************** -->
        <device Dname="STM32G473RCTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="QFP" n="64"/>
        </device>

        <!-- *************************  Device 'STM32G473VC'  ***************************** -->
        <device Dname="STM32G473VCTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="QFP" n="100"/>
        </device>
        <device Dname="STM32G473VCHx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="BGA" n="100"/>
        </device>
        <device Dname="STM32G473VCIx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="BGA" n="100"/>
        </device>

        <!-- #################################### 128 KB ################################# -->
        <!-- *************************  Device 'STM32G473CB'  ***************************** -->
        <device Dname="STM32G473CBTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>
        <device Dname="STM32G473CBUx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>

        <!-- *************************  Device 'STM32G473MB'  ***************************** -->
        <device Dname="STM32G473MBTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="80"/>
        </device>

        <!-- *************************  Device 'STM32G473QB'  ***************************** -->
        <device Dname="STM32G473QBTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="128"/>
        </device>

        <!-- *************************  Device 'STM32G473RB'  ***************************** -->
        <device Dname="STM32G473RBTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="64"/>
        </device>

        <!-- *************************  Device 'STM32G473VB'  ***************************** -->
        <device Dname="STM32G473VBTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="100"/>
        </device>
        <device Dname="STM32G473VBHx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="BGA" n="100"/>
        </device>
        <device Dname="STM32G473VBIx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="BGA" n="100"/>
        </device>
      </subFamily>

      <!-- ************************  Subfamily 'STM32G474'  **************************** -->
      <subFamily DsubFamily="STM32G474">
        <processor Dclock="170000000" />
        <compile header="Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h" define="STM32G474xx"/>
        <debugvars configfile="CMSIS/Debug/STM32G4xx.dbgconf">
          __var DbgMCU_CR       = 0x00000007;   // DBGMCU_CR: DBG_SLEEP, DBG_STOP, DBG_STANDBY
          __var DbgMCU_APB1_Fz1 = 0x00000000;   // DGBMCU_APB1_FZ1: All Peripherals Operate as in Normal Mode
          __var DbgMCU_APB1_Fz2 = 0x00000000;   // DGBMCU_APB1_FZ2: All Peripherals Operate as in Normal Mode
          __var DbgMCU_APB2_Fz  = 0x00000000;   // DGBMCU_APB2_FZ: All Peripherals Operate as in Normal Mode
          __var TraceClk_Pin    = 0x00040002;   // PE2
          __var TraceD0_Pin     = 0x00040003;   // PE3
          __var TraceD1_Pin     = 0x00040004;   // PE4
          __var TraceD2_Pin     = 0x00040005;   // PE5
          __var TraceD3_Pin     = 0x00040006;   // PE6
        </debugvars>
        <debug svd="CMSIS/SVD/STM32G474xx.svd"/>

        <book name="Documentation/DS12288.pdf" title="STM32G474xx Data Sheet"/>

        <memory id="IRAM1" start="0x20000000" size="0x00020000" init="0" default="1" />

        <!-- ################################### 512 KB ################################## -->
        <!-- *************************  Device 'STM32G474CE'  ***************************** -->
        <device Dname="STM32G474CETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="48"/>
        </device>
        <device Dname="STM32G474CEUx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="48"/>
        </device>

        <!-- *************************  Device 'STM32G474RE'  ***************************** -->
        <device Dname="STM32G474RETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="64"/>
        </device>

        <!-- *************************  Device 'STM32G474VE'  ***************************** -->
        <device Dname="STM32G474VETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="100"/>
        </device>
        <device Dname="STM32G474VEHx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="BGA" n="100"/>
        </device>
        <device Dname="STM32G474VEIx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="BGA" n="100"/>
        </device>

        <!-- *************************  Device 'STM32G474ME'  ***************************** -->
        <device Dname="STM32G474METx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="80"/>
        </device>
        <device Dname="STM32G474MEYx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="CSP" n="81"/>
        </device>

        <!-- *************************  Device 'STM32G474QE'  ***************************** -->
        <device Dname="STM32G474QETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="128"/>
        </device>

        <!-- ################################### 256 KB ################################## -->
        <!-- *************************  Device 'STM32G474CC'  ***************************** -->
        <device Dname="STM32G474CCTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>
        <device Dname="STM32G474CCUx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>

        <!-- *************************  Device 'STM32G474MC'  ***************************** -->
        <device Dname="STM32G474MCTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="QFP" n="80"/>
        </device>

        <!-- *************************  Device 'STM32G474QC'  ***************************** -->
        <device Dname="STM32G474QCTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="QFP" n="128"/>
        </device>

         <!-- *************************  Device 'STM32G474RC'  ***************************** -->
        <device Dname="STM32G474RCTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="QFP" n="64"/>
        </device>

        <!-- *************************  Device 'STM32G474VC'  ***************************** -->
        <device Dname="STM32G474VCTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="QFP" n="100"/>
        </device>
        <device Dname="STM32G474VCHx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="BGA" n="100"/>
        </device>
        <device Dname="STM32G474VCIx">
          <memory id="IROM1"                              start="0x08000000" size="0x00040000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_256.FLM" start="0x08000000" size="0x00040000"             default="1" />
          <feature type="BGA" n="100"/>
        </device>

        <!-- #################################### 128 KB ################################## -->
        <!-- *************************  Device 'STM32G474CB'  ***************************** -->
        <device Dname="STM32G474CBTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>
        <device Dname="STM32G474CBUx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>

        <!-- *************************  Device 'STM32G474RB'  ***************************** -->
        <device Dname="STM32G474RBTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="64"/>
        </device>

        <!-- *************************  Device 'STM32G474VB'  ***************************** -->
        <device Dname="STM32G474VBHx">
         <memory id="IROM1"                               start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="BGA" n="100"/>
        </device>
        <device Dname="STM32G474VBIx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="BGA" n="100"/>
        </device>
        <device Dname="STM32G474VBTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="100"/>
        </device>

        <!-- *************************  Device 'STM32G474QB'  ***************************** -->
        <device Dname="STM32G474QBTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="128"/>
        </device>

        <!-- *************************  Device 'STM32G474MB'  ***************************** -->
        <device Dname="STM32G474MBTx">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="80"/>
        </device>
      </subFamily>

      <!-- ************************  Subfamily 'STM32G483'  **************************** -->
      <subFamily DsubFamily="STM32G483">
        <processor Dclock="170000000" />
        <compile header="Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h" define="STM32g484xx"/>
        <debugvars configfile="CMSIS/Debug/STM32G4xx.dbgconf">
          __var DbgMCU_CR       = 0x00000007;   // DBGMCU_CR: DBG_SLEEP, DBG_STOP, DBG_STANDBY
          __var DbgMCU_APB1_Fz1 = 0x00000000;   // DGBMCU_APB1_FZ1: All Peripherals Operate as in Normal Mode
          __var DbgMCU_APB1_Fz2 = 0x00000000;   // DGBMCU_APB1_FZ2: All Peripherals Operate as in Normal Mode
          __var DbgMCU_APB2_Fz  = 0x00000000;   // DGBMCU_APB2_FZ: All Peripherals Operate as in Normal Mode
          __var TraceClk_Pin    = 0x00040002;   // PE2
          __var TraceD0_Pin     = 0x00040003;   // PE3
          __var TraceD1_Pin     = 0x00040004;   // PE4
          __var TraceD2_Pin     = 0x00040005;   // PE5
          __var TraceD3_Pin     = 0x00040006;   // PE6
        </debugvars>
        <debug svd="CMSIS/SVD/STM32G483xx.svd"/>

        <book name="Documentation/DS12997.pdf" title="STM32G483xx Data Sheet"/>

        <memory id="IRAM1" start="0x20000000" size="0x00020000" init="0" default="1" />

        <!-- #################################### 512 KB ################################# -->			
        <!-- *************************  Device 'STM32G483CE'  ***************************** -->
        <device Dname="STM32G483CETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="48"/>
        </device>
        <device Dname="STM32G483CEUx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="48"/>
        </device>
        
        <!-- *************************  Device 'STM32G483ME'  ***************************** -->
        <device Dname="STM32G483METx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="80"/>
        </device>
        <device Dname="STM32G483MEYx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="CSP" n="49"/>
        </device>
        
        <!-- *************************  Device 'STM32G483RE'  ***************************** -->
        <device Dname="STM32G483RETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="64"/>
        </device>
        
        <!-- *************************  Device 'STM32G483VE'  ***************************** -->
        <device Dname="STM32G483VETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="100"/>
        </device>
        <device Dname="STM32G483VEHx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="BGA" n="100"/>
        </device>
        <device Dname="STM32G483VEIx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="BGA" n="100"/>
        </device> 
        
        <!-- *************************  Device 'STM32G483QE'  ***************************** -->
        <device Dname="STM32G483QETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="128"/>
        </device>
      </subFamily>

      <!-- ************************  Subfamily 'STM32G484'  **************************** -->
      <subFamily DsubFamily="STM32G484">
        <processor Dclock="150000000" />
        <compile header="Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h" define="STM32G484xx"/>
        <debugvars configfile="CMSIS/Debug/STM32G4xx.dbgconf">
          __var DbgMCU_CR       = 0x00000007;   // DBGMCU_CR: DBG_SLEEP, DBG_STOP, DBG_STANDBY
          __var DbgMCU_APB1_Fz1 = 0x00000000;   // DGBMCU_APB1_FZ1: All Peripherals Operate as in Normal Mode
          __var DbgMCU_APB1_Fz2 = 0x00000000;   // DGBMCU_APB1_FZ2: All Peripherals Operate as in Normal Mode
          __var DbgMCU_APB2_Fz  = 0x00000000;   // DGBMCU_APB2_FZ: All Peripherals Operate as in Normal Mode
          __var TraceClk_Pin    = 0x00040002;   // PE2
          __var TraceD0_Pin     = 0x00040003;   // PE3
          __var TraceD1_Pin     = 0x00040004;   // PE4
          __var TraceD2_Pin     = 0x00040005;   // PE5
          __var TraceD3_Pin     = 0x00040006;   // PE6
        </debugvars>
        <debug svd="CMSIS/SVD/STM32G484xx.svd"/>

        <book name="Documentation/DocID031193.pdf" title="STM32G484xx Data Brief"/>

        <memory id="IRAM1" start="0x20000000" size="0x00020000" init="0" default="1" />

        <!-- #################################### 512 KB ################################# -->
        <!-- *************************  Device 'STM32G484CE'  ***************************** -->
        <device Dname="STM32G484CETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="48"/>
        </device>
        <device Dname="STM32G484CEUx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="48"/>
        </device>

        <!-- *************************  Device 'STM32G484RE'  ***************************** -->
        <device Dname="STM32G484RETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="64"/>
        </device>

        <!-- *************************  Device 'STM32G484VE'  ***************************** -->
        <device Dname="STM32G484VETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="100"/>
        </device>
        <device Dname="STM32G484VEIx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="BGA" n="100"/>
        </device>
        <device Dname="STM32G484VEHx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="BGA" n="100"/>
        </device>

        <!-- *************************  Device 'STM32G484ME'  ***************************** -->
        <device Dname="STM32G484METx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="80"/>
        </device>
        <device Dname="STM32G484MEYx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="81"/>
        </device>

        <!-- *************************  Device 'STM32G484QE'  ***************************** -->
        <device Dname="STM32G484QETx">
          <memory id="IROM1"                                   start="0x08000000" size="0x00080000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512_Dual.FLM" start="0x08000000" size="0x00080000"             default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_512.FLM"      start="0x08000000" size="0x00080000"             default="0" />
          <feature type="QFP" n="128"/>
        </device>
      </subFamily>

      <!-- ************************  Subfamily 'STM32GBK1'  **************************** -->
      <subFamily DsubFamily="STM32GBK1">
        <compile header="Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h" define="STM32GBK1CB"/>
        <debugvars configfile="CMSIS/Debug/STM32G4xx.dbgconf">
          __var DbgMCU_CR       = 0x00000007;   // DBGMCU_CR: DBG_SLEEP, DBG_STOP, DBG_STANDBY
          __var DbgMCU_APB1_Fz1 = 0x00000000;   // DGBMCU_APB1_FZ1: All Peripherals Operate as in Normal Mode
          __var DbgMCU_APB1_Fz2 = 0x00000000;   // DGBMCU_APB1_FZ2: All Peripherals Operate as in Normal Mode
          __var DbgMCU_APB2_Fz  = 0x00000000;   // DGBMCU_APB2_FZ: All Peripherals Operate as in Normal Mode
          __var TraceClk_Pin    = 0x00040002;   // PE2
          __var TraceD0_Pin     = 0x00040003;   // PE3
          __var TraceD1_Pin     = 0x00040004;   // PE4
          __var TraceD2_Pin     = 0x00040005;   // PE5
          __var TraceD3_Pin     = 0x00040006;   // PE6
        </debugvars>
        <debug svd="CMSIS/SVD/STM32GBK1CBT6.svd"/>

        <book name="Documentation/DS12712.pdf" title="STM32G473xx Data Sheet"/>

        <memory id="IRAM1" start="0x20000000" size="0x00008000" init="0" default="1" />

        <!-- ################################### 128 KB ################################### -->
        <!-- *************************  Device 'STM32GBK1CB'  ***************************** -->
        <device Dname="STM32GBK1CB">
          <memory id="IROM1"                              start="0x08000000" size="0x00020000" startup="1" default="1" />
          <algorithm name="CMSIS/Flash/STM32G4xx_128.FLM" start="0x08000000" size="0x00020000"             default="1" />
          <feature type="QFP" n="48"/>
        </device>
      </subFamily>
    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler ARMCC">
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>
    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="STM32G4">
      <description>STMicroelectronics STM32G4 Devices</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32G4*"/>
    </condition>

    <condition id="STM32G431">
      <description>STMicroelectronics STM32G431 Devices</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32G431*"/>
    </condition>
    <condition id="STM32G441">
      <description>STMicroelectronics STM32G441 Devices</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32G441*"/>
    </condition>
    <condition id="STM32G471">
      <description>STMicroelectronics STM32G471 Devices</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32G471*"/>
    </condition>
    <condition id="STM32G473">
      <description>STMicroelectronics STM32G473 Devices</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32G473*"/>
    </condition>
    <condition id="STM32G474">
      <description>STMicroelectronics STM32G474 Devices</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32G474*"/>
    </condition>
    <condition id="STM32G483">
      <description>STMicroelectronics STM32G483 Devices</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32G483*"/>
    </condition>
    <condition id="STM32G484">
      <description>STMicroelectronics STM32G484 Devices</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32G484*"/>
    </condition>
    <condition id="STM32GBK1">
      <description>STMicroelectronics STM32GBK1 Devices</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32GBK1*"/>
    </condition>

    <!-- Device + CMSIS + STM32CubeMX Conditions -->
    <condition id="STM32G4 CMSIS STM32CubeMX">
      <description>STMicroelectronics STM32G4 Devices, CMSIS-Core and STM32CubeMX</description>
      <require condition="STM32G4"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
      <require Cbundle="STM32CubeMX" Cclass="Device" Cgroup="STM32Cube Framework" Csub="STM32CubeMX"/>
    </condition>

    <condition id="STM32G474 CMSIS STM32CubeMX">
      <description>STMicroelectronics STM32G474 Devices, CMSIS-Core and STM32CubeMX</description>
      <require condition="STM32G474"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
      <require Cbundle="STM32CubeMX" Cclass="Device" Cgroup="STM32Cube Framework" Csub="STM32CubeMX"/>
    </condition>

    <!-- STM32G474E-EVAL BSP Conditions -->
    <condition id="STM32G474E-EVAL BSP">
      <description>STMicroelectronics STM32G474E-EVAL BSP</description>
      <require condition="STM32G474 CMSIS STM32CubeMX"/>
      <require Cclass="Board Support" Cgroup="Drivers" Csub="I/O"/>
      <require Cclass="Board Support" Cgroup="Drivers" Csub="BUS"/>
    </condition>
    <condition id="STM32G474E-EVAL BSP Audio">
      <description>STMicroelectronics STM32G474E-EVAL BSP Audio</description>
      <require condition="STM32G474 CMSIS STM32CubeMX"/>
      <require Cclass="Board Support" Cgroup="Drivers" Csub="Basic I/O"/>
    </condition>
    <condition id="STM32G474E-EVAL BSP BUS">
      <description>STMicroelectronics STM32G474E-EVAL BSP BUS</description>
      <require condition="STM32G474 CMSIS STM32CubeMX"/>
      <require Cclass="Board Support" Cgroup="Drivers" Csub="Basic I/O"/>
    </condition>
    <condition id="STM32G474E-EVAL BSP ENV-Sensor">
      <description>STMicroelectronics STM32G474E-EVAL BSP ENV-Sensor</description>
      <require condition="STM32G474 CMSIS STM32CubeMX"/>
      <require Cclass="Board Support" Cgroup="Drivers" Csub="Basic I/O"/>
    </condition>
    <condition id="STM32G474E-EVAL BSP IDD">
      <description>STMicroelectronics STM32G474E-EVAL BSP IDD</description>
      <require condition="STM32G474 CMSIS STM32CubeMX"/>
      <require Cclass="Board Support" Cgroup="Drivers" Csub="Basic I/O"/>
    </condition>
    <condition id="STM32G474E-EVAL BSP IO">
      <description>STMicroelectronics STM32G474E-EVAL BSP IO</description>
      <require condition="STM32G474 CMSIS STM32CubeMX"/>
      <require Cclass="Board Support" Cgroup="Drivers" Csub="Basic I/O"/>
    </condition>
    <condition id="STM32G474E-EVAL BSP LCD">
      <description>STMicroelectronics STM32G474E-EVAL BSP LCD</description>
      <require condition="STM32G474 CMSIS STM32CubeMX"/>
      <require Cclass="Board Support" Cgroup="Drivers" Csub="Basic I/O"/>
    </condition>
    <condition id="STM32G474E-EVAL BSP QSPI">
      <description>STMicroelectronics STM32L496G-Discovery BSP QSPI</description>
      <require condition="STM32G474 CMSIS STM32CubeMX"/>
    </condition>
    <condition id="STM32G474E-EVAL BSP SD">
      <description>STMicroelectronics STM32G474E-EVAL BSP SD</description>
      <require condition="STM32G474 CMSIS STM32CubeMX"/>
      <require Cclass="Board Support" Cgroup="Drivers" Csub="I/O"/>
    </condition>
    <condition id="STM32G474E-EVAL BSP Smartcard">
      <description>STMicroelectronics STM32G474E-EVAL BSP Smartcard</description>
      <require condition="STM32G474 CMSIS STM32CubeMX"/>
      <require Cclass="Board Support" Cgroup="Drivers" Csub="I/O"/>
    </condition>
    <condition id="STM32G474E-EVAL BSP SRAM">
      <description>STMicroelectronics STM32G474E-EVAL BSP SRAM</description>
      <require condition="STM32G474 CMSIS STM32CubeMX"/>
    </condition>
    <condition id="STM32G474E-EVAL BSP USBPD">
      <description>STMicroelectronics STM32G474E-EVAL BSP USBPD</description>
      <require condition="STM32G474 CMSIS STM32CubeMX"/>
    </condition>
    <condition id="STM32G474E-EVAL LED">
      <description>STMicroelectronics STM32G474E-EVAL LED</description>
      <require condition="STM32G474 CMSIS STM32CubeMX"/>
      <require Cclass="Board Support" Cgroup="Drivers" Csub="Basic I/O"/>
    </condition>
    <condition id="STM32G474E-EVAL Buttons">
      <description>STMicroelectronics STM32G474E-EVAL Buttons</description>
      <require condition="STM32G474 CMSIS STM32CubeMX"/>
      <require Cclass="Board Support" Cgroup="Drivers" Csub="Basic I/O"/>
    </condition>
    <condition id="STM32G474E-EVAL Joystick">
      <description>STMicroelectronics STM32G474E-EVAL Joystick</description>
      <require condition="STM32G474 CMSIS STM32CubeMX"/>
      <require Cclass="Board Support" Cgroup="Drivers" Csub="Basic I/O"/>
    </condition>
    <condition id="STM32G474E-EVAL ADC">
      <description>STMicroelectronics STM32G474E-EVAL ADC</description>
      <require condition="STM32G474 CMSIS STM32CubeMX"/>
    </condition>
  </conditions>

  <generators>
    <!-- This generator is launched if any component referencing this generator by 'id' is selected and the specified <gpdsc> file does not exist -->
    <generator id="STM32CubeMX">
      <description>ST Microelectronics: STCubeMX Environment</description>
      <command>MDK/CubeMX/STM32CubeMxLauncher.exe</command> <!-- path is specified either absolute or relative to PDSC or GPDSC file -->
      <workingDir>$PRTE/Device</workingDir> <!-- path is specified either absolute or relative to PDSC or GPDSC file. If not specified it is the project directory configured by the environment -->
      <arguments>
        <!-- D = Device (Dname/Dvariant as configured by environment) -->
        <argument>$D</argument>
        <!-- Project path and project name (as configured by environment) -->
        <argument>#P</argument>
        <!-- absolute or relative to workingDir. $S = Device Family Pack base folder -->
        <argument>$S</argument>
      </arguments>
      <!-- path is either absolute or relative to working directory -->
      <gpdsc name="$PRTE/Device/$D/FrameworkCubeMX.gpdsc"/>
    </generator>
  </generators>

  <taxonomy>
    <description Cclass="Device" Cgroup="STM32Cube HAL">STM32G4xx Hardware Abstraction Layer (HAL) Drivers</description>
  </taxonomy>

  <apis>
    <api Cclass="Device" Cgroup="STM32Cube Framework" Capiversion="1.1.0" exclusive="1">
      <description>STM32Cube Framework</description>
      <files>
        <file category="doc" name="MDK/CubeMX/Documentation/index.html"/>
      </files>
    </api>
  </apis>

  <components>
    <bundle Cbundle="STM32CubeMX" Cclass="Device" isDefaultVariant="true" Cversion="1.0.0">
      <description>Peripheral, Clock and Pin configuration are managed by STM32CubeMX</description>
      <doc>MDK/CubeMX/Documentation/index.html</doc>
      <component Cgroup="Startup" Cversion="1.0.0" condition="STM32G4 CMSIS STM32CubeMX">  <!-- Cversion is necessary -->
        <description>System Startup for STMicroelectronics STM32G4 Series</description>
        <RTE_Components_h>
          <!-- the following content goes into file 'RTE_Components.h' -->
          #define RTE_DEVICE_STARTUP_STM32G4XX    /* Device Startup for STM32G4 */
        </RTE_Components_h>

        <files>
          <!-- Flash Option Bytes templates -->
          <!--file category="source"   name="MDK/Device/Source/ARM/STM32G4xx_OPT.s"       attr="template" version="1.0.0" condition="Compiler ARMCC" select="Flash Option Bytes"/-->
        </files>
      </component>

      <component generator="STM32CubeMX" Cgroup="STM32Cube Framework" Csub="STM32CubeMX" Cversion="1.0.0" condition="STM32G4">
        <description>Configuration via STM32CubeMX</description>
        <RTE_Components_h>
          #define RTE_DEVICE_FRAMEWORK_CUBE_MX
        </RTE_Components_h>
        <files>
          <file category="doc"     name="MDK/CubeMX/Documentation/cubemx.html"/>
          <file category="source"  name="MDK/CubeMX/run_STM32CubeMX.c"         version="1.0.0"/>
          <file category="other"   name="MDK/CubeMX/FrameworkCubeMX_gpdsc.ftl" version="1.0.0"/>
          <file category="other"   name="MDK/CubeMX/MX_Device_h.ftl"           version="1.0.2"/>
        </files>
      </component>

      <component Cgroup="STM32Cube HAL" Cversion="1.10.0" condition="STM32G4 CMSIS STM32CubeMX">
        <description>STM32Cube HAL Drivers</description>
        <RTE_Components_h>
          #define RTE_DEVICE_CUBE_MX_HAL
        </RTE_Components_h>
        <files>
          <file category="source"  name="MDK/Driver/stm32g4xx_ll.c"/>
          <file category="include" name="Drivers/STM32G4xx_HAL_Driver/Inc/"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc_ex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_comp.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_cordic.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_cortex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_crc.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_crc_ex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_cryp.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_cryp_ex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dac.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dac_ex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma_ex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_exti.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_fdcan.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ramfunc.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_fmac.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_gpio.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_hrtim.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_i2c.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_i2c_ex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_i2s.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_irda.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_iwdg.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_lptim.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_nand.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_nor.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_opamp.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_opamp_ex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pcd.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pcd_ex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr_ex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_qspi.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc_ex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rng.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rtc.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rtc_ex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_sai.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_sai_ex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_smartcard.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_smartcard_ex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_smbus.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_spi.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_spi_ex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_sram.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim_ex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart_ex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_usart.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_usart_ex.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_wwdg.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_adc.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_comp.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_cordic.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_crc.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_crs.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_dac.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_dma.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_exti.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_fmac.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_fmc.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_gpio.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_hrtim.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_i2c.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_lptim.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_lpuart.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_opamp.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_pwr.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_rcc.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_rng.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_rtc.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_spi.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_tim.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_ucpd.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_usart.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_usb.c"/>
          <file category="source"  name="Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_utils.c"/>
        </files>
      </component>
    </bundle>

    <!-- CMSIS drivers -->


    <!-- STM32G474E-EVAL Board Support -->
    <bundle Cbundle="STM32G474E-EVAL" Cclass="Board Support" Cversion="1.0.0">
      <description>STMicroelectronics STM32G474E-EVAL Board</description>
      <!--doc>MDK/Boards/ST/STM32G474E-EVAL/Documentation/?.pdf</doc-->
      <doc></doc>
      <component Cgroup="Drivers" Csub="Basic I/O"          condition="STM32G474E-EVAL BSP">
        <description>LEDs, push-buttons and COM ports for STMicroelectronics STM32G474E-EVAL Board</description>
        <files>
          <file category="header" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval.h"/>
          <file category="source" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval.c"/>
          <file category="header" name="MDK/Boards/ST/STM32G474E-EVAL/ConfigTemplate/stm32g474e_eval_conf.h" attr="config" version="1.0.0"/>
        </files>
      </component>
      <component Cgroup="Drivers" Csub="Audio"              condition="STM32G474E-EVAL BSP Audio">
        <description>Audio for STMicroelectronics STM32G474E-EVAL Board</description>
        <files>
          <file category="header" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_audio.h"/>
          <file category="source" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_audio.c"/>
          <file category="source" name="Drivers/BSP/Components/wm8994/wm8994.c"/>
          <file category="source" name="Drivers/BSP/Components/wm8994/wm8994_reg.c"/>
        </files>
      </component>
      <component Cgroup="Drivers" Csub="BUS"                condition="STM32G474E-EVAL BSP BUS">
        <description>BUS for STMicroelectronics STM32G474E-EVAL Board</description>
        <files>
          <file category="header" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_bus.h"/>
          <file category="source" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_bus.c"/>
        </files>
      </component>
      <component Cgroup="Drivers" Csub="ENV-Sensor"         condition="STM32G474E-EVAL BSP ENV-Sensor">
        <description>ENV-SensorD for STMicroelectronics STM32G474E-EVAL Board</description>
        <files>
          <file category="header" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_env_sensor.h"/>
          <file category="source" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_env_sensor.c"/>
        </files>
      </component>
      <component Cgroup="Drivers" Csub="IDD"                condition="STM32G474E-EVAL BSP IDD">
        <description>IDD for STMicroelectronics STM32G474E-EVAL Board</description>
        <files>
          <file category="header" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_idd.h"/>
          <file category="source" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_idd.c"/>
        </files>
      </component>
      <component Cgroup="Drivers" Csub="I/O"                condition="STM32G474E-EVAL BSP IO">
        <description>I/O for STMicroelectronics STM32G474E-EVAL Board</description>
        <files>
          <file category="header" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_io.h"/>
          <file category="source" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_io.c"/>
          <file category="source" name="Drivers/BSP/Components/mfxstm32l152/mfxstm32l152.c"/>
          <file category="source" name="Drivers/BSP/Components/mfxstm32l152/mfxstm32l152_reg.c"/>
        </files>
      </component>
      <component Cgroup="Drivers" Csub="LCD"                condition="STM32G474E-EVAL BSP LCD">
        <description>LCD for STMicroelectronics STM32G474E-EVAL Board</description>
        <files>
          <file category="header" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_lcd.h"/>
          <file category="source" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_lcd.c"/>
          <file category="source" name="Drivers/BSP/Components/hx8347d/hx8347d.c"/>
          <file category="source" name="Drivers/BSP/Components/hx8347d/hx8347d_regs.c"/>
        </files>
      </component>
      <component Cgroup="Drivers" Csub="QSPI"               condition="STM32G474E-EVAL BSP QSPI">
        <description>QSPI Flash for STMicroelectronics STM32G474E-EVAL Board</description>
        <files>
          <file category="header" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_qspi.h"/>
          <file category="source" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_qspi.c"/>
        </files>
      </component>
      <component Cgroup="Drivers" Csub="SD"                 condition="STM32G474E-EVAL BSP SD">
        <description>uSD for STMicroelectronics STM32G474E-EVAL Board</description>
        <files>
          <file category="header" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_sd.h"/>
          <file category="source" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_sd.c"/>
        </files>
      </component>
      <component Cgroup="Drivers" Csub="Smartcard"          condition="STM32G474E-EVAL BSP Smartcard">
        <description>uSD for STMicroelectronics STM32G474E-EVAL Board</description>
        <files>
          <file category="header" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_smartcard.h"/>
          <file category="source" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_smartcard.c"/>
        </files>
      </component>
      <component Cgroup="Drivers" Csub="SRAM"               condition="STM32G474E-EVAL BSP SRAM">
        <description>SRAM for STMicroelectronics STM32G474E-EVAL Board</description>
        <files>
          <file category="header" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_sram.h"/>
          <file category="source" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_sram.c"/>
        </files>
      </component>
      <component Cgroup="Drivers" Csub="USBPD"              condition="STM32G474E-EVAL BSP USBPD">
        <description>USB Type-C / USB Power Delivery for STMicroelectronics STM32G474E-EVAL Board</description>
        <files>
          <file category="header" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_usbpd_pwr.h"/>
          <file category="source" name="Drivers/BSP/STM32G474E-EVAL/stm32g474e_eval_usbpd_pwr.c"/>
        </files>
      </component>
      <component Cgroup="LED"           Capiversion="1.0.0" condition="STM32G474E-EVAL LED">
      <description>LED driver for STMicroelectronics STM32G474E-EVAL Board</description>
        <files>
          <file category="source" name="MDK/Boards/ST/STM32G474E-EVAL/Common/LED_STM32G474E-EVAL.c"/>
        </files>
      </component>
      <component Cgroup="Buttons"       Capiversion="1.0.0" condition="STM32G474E-EVAL Buttons">
      <description>Button driver for STMicroelectronics STM32G474E-EVAL Board</description>
        <files>
        <file category="sourceC" name="MDK/Boards/ST/STM32G474E-EVAL/Common/Buttons_STM32G474E-EVAL.c"/>
        </files>
      </component>
      <component Cgroup="Joystick"      Capiversion="1.0.0" condition="STM32G474E-EVAL Joystick">
        <description>Joystick Interface for STMicroelectronics STM32G474E-EVAL Board</description>
        <files>
          <file category="source" name="MDK/Boards/ST/STM32G474E-EVAL/Common/Joystick_STM32G474E-EVAL.c"/>
        </files>
      </component>
<!--
      <component Cgroup="A/D Converter" Capiversion="1.0.0" condition="STM32G474E-EVAL ADC">
        <description>A/D Converter Interface for STMicroelectronics STM32G474E-EVAL Board</description>
        <files>
          <file category="source" name="MDK/Boards/ST/STM32G474E-EVAL/Common/ADC_STM32G474E-EVAL.c"/>
        </files>
      </component>
-->
    </bundle>

  </components>

  <boards>
    <!-- STM32G474E-EVAL Board Support -->
    <board vendor="STMicroelectronics" name="STM32G474E-EVAL" revision="Rev.A" salesContact="http://www.st.com/stonline/contactus/contacts/index.php">
      <description>STMicroelectronics STM32G474E-EVAL</description>
<!--
      <image small="MDK/Boards/ST/STM32G474E-EVAL/Documentation/stm32g474e-eval_small.jpg"
             large="MDK/Boards/ST/STM32G474E-EVAL/Documentation/stm32g474e-eval_large.jpg"/>
      <book category="overview"  name="http://www.st.com/web/en/catalog/tools/PF261634" title="Web Page"/>
      <book category="schematic" name="MDK/Boards/ST/STM32G474E-EVAL/Documentation/?.pdf" title="Schematics"/>
      <book category="manual"    name="MDK/Boards/ST/STM32G474E-EVAL/Documentation/?.pdf" title="User Manual"/>
-->
      <mountedDevice    deviceIndex="0" Dvendor="STMicroelectronics:13" Dname="STM32G474QETx"/>
      <compatibleDevice deviceIndex="0" Dvendor="STMicroelectronics:13" DsubFamily="STM32G474"/>
<!--
      <feature type="XTAL"            n="8000000"/>
      <feature type="PWR"             n="5"              name="USB Powered"/>
      <feature type="USB"             n="1"              name="USB 2.0 Full Speed"/>
      <feature type="CAN"             n="1"/>
      <feature type="RS232"           n="1"/>
      <feature type="GLCD"            n="1"  m="240.320" name="2.4 inch Color QVGA TFT LCD with resistive touchscreen"/>
      <feature type="Joystick"        n="1"              name="5-position Joystick"/>
      <feature type="Poti"            n="1"              name="Analog Voltage Control for ADC Input (potentiometer)"/>
      <feature type="Button"          n="4"              name="Push-Buttons for Reset, Tamper and User"/>
      <feature type="LED"             n="4"              name="LEDs directly connected to port pins"/>
-->
      <debugInterface adapter="JTAG/SW" connector="20 pin JTAG (0.1 inch connector)"/>
      <debugInterface adapter="JTAG/SW" connector="20-pin Cortex debug + ETM Trace (0.05 inch connector)"/>
    </board>
  </boards>

  <examples>
    <!-- STM32G474E-EVAL Board -->
    <example name="CMSIS-RTOS2 Blinky" doc="Abstract.txt" folder="MDK/Boards/ST/STM32G474E-EVAL/Blinky">
      <description>CMSIS-RTOS2 Blinky example</description>
      <board name="STM32G474E-EVAL" vendor="STMicroelectronics"/>
      <project>
        <environment name="uv" load="Blinky.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="CMSIS" Cgroup="CORE"/>
        <component Cclass="Device" Cgroup="Startup"/>
        <component Cclass="CMSIS" Cgroup="RTOS"/>
        <category>Getting Started</category>
        <category>CMSIS-RTX</category>
      </attributes>
    </example>
  </examples>

</package>
